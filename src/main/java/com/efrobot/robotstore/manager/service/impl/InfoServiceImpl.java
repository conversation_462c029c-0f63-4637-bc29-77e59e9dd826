package com.efrobot.robotstore.manager.service.impl;

import com.efrobot.robotstore.baseapi.manager.pojo.fabo.enums.CommissionType;
import com.efrobot.robotstore.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.efrobot.robotstore.baseapi.manager.fabo.*;
import com.efrobot.robotstore.baseapi.manager.pojo.SchoolTypeEnum;
import com.efrobot.robotstore.baseapi.manager.pojo.fabo.*;
import com.efrobot.robotstore.baseapi.manager.pojo.robot.Robot;
import com.efrobot.robotstore.baseapi.manager.pojo.sn.LLogisticsManager;
import com.efrobot.robotstore.baseapi.manager.pojo.sn.LProvince;
import com.efrobot.robotstore.baseapi.manager.pojo.sys.SysUser;
import com.efrobot.robotstore.baseapi.manager.robot.RobotMapper;
import com.efrobot.robotstore.baseapi.manager.sn.LLogisticsManagerMapper;
import com.efrobot.robotstore.baseapi.manager.sn.LProvinceMapper;
import com.efrobot.robotstore.baseapi.manager.sys.CoursewareInfoUploadMapper;
import com.efrobot.robotstore.manager.service.InfoService;
import com.efrobot.robotstore.util.*;
import com.github.pagehelper.PageHelper;
import org.apache.log4j.Logger;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service("infoService")
@Transactional
public class InfoServiceImpl implements InfoService {
    private static final Logger log = Logger.getLogger(InfoServiceImpl.class);
    @Resource
    private RobotMapper robotMapper;
    @Resource
    private MemberMapper memberMapper;
    @Resource
    private YproductMapper yproductMapper;
    @Resource
    private ZsaleMapper zsaleMapper;
    @Resource
    private YcostMapper ycostMapper;
    @Resource
    private YorderDetailsMapper yorderDetailsMapper;
    @Resource
    private YpaidDetailsMapper ypaidDetailsMapper;
    @Resource
    private YcollectionMapper ycollectionMapper;
    @Resource
    private XfaboSmsMapper xfaboSmsMapper;
    @Resource
    private YdepartProfitMapper ydepartProfitMapper;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private ZagentsMapper zagentsMapper;
    @Resource
    private ZrobotMapper zrobotMapper;
    @Resource
    private LProvinceMapper lProvinceMapper;
    @Resource
    private SchoolRobotMapper schoolRobotMapper;
    @Resource
    private CoursewareInfoUploadMapper coursewareInfoUploadMapper;
    @Resource
    private LLogisticsManagerMapper lLogisticsManagerMapper;
    @Resource
    private YcontractMapper ycontractMapper;
    @Resource
    private YinvoiceMapper yinvoiceMapper;
    @Resource
    private YcontractPdfMapper ycontractPdfMapper;
    @Resource
    private YbondMapper ybondMapper;
    @Resource
    private LogMapper logMapper;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private ZAgentBalanceMapper zAgentBalanceMapper;
    @Resource
    private CommissionRatioConfigMapper commissionRatioConfigMapper;
    @Resource
    private XDepartmentCommonlyMapper departmentCommonlyMapper;

    public PageInfo<Zsale> getZsaleInfoPage(Zsale info, Integer pageNum, Integer pageSize) throws Exception {

        PageHelper.startPage(pageNum.intValue(), pageSize.intValue());

        List<Zsale> list = this.zsaleMapper.selectByParms(info);

        Subject subject = SecurityUtils.getSubject();

        Session session = subject.getSession();

        SysUser sysUser = (SysUser) session.getAttribute("sessionUser");

        if (sysUser.getUsername().equals("aftersale")) {

            for (Zsale zs : list) {

                zs.setSchoolName("******");

                zs.setDepartment("******");

                zs.setSalesManager("******");

                zs.setSalesPhone("******");

                zs.setConsigneePhone("******");

                zs.setConsigneeAdress("******");

                zs.setConsignee("******");
            }
        }

        PageInfo<Zsale> page = new PageInfo(list);

        return page;
    }


    public PageInfo<YorderDetails> getYorderDetailsPage(YorderDetails info, Integer pageNum, Integer pageSize) throws Exception {

        PageHelper.startPage(pageNum.intValue(), pageSize.intValue());

        List<YorderDetails> list = this.yorderDetailsMapper.selectByParms(info);


        PageInfo<YorderDetails> page = new PageInfo(list);

        return page;
    }


    public PageInfo<YpaidDetails> getYpaidDetailsPage(YpaidDetails info, Integer pageNum, Integer pageSize) throws Exception {

        PageHelper.startPage(pageNum.intValue(), pageSize.intValue());

        List<YpaidDetails> list = this.ypaidDetailsMapper.selectByParms(info);

        PageInfo<YpaidDetails> page = new PageInfo(list);

        return page;
    }


    public List<YorderDetails> selectByParms(YorderDetails record) {

        List<YorderDetails> list = this.yorderDetailsMapper.selectByParms(record);

        return list;
    }


    public PageInfo<Student> getStudentInfoPage(Student info, Integer pageNum, Integer pageSize) throws Exception {

        PageHelper.startPage(pageNum.intValue(), pageSize.intValue());

        List<Student> list = this.studentMapper.selectByParms(info);


        PageInfo<Student> page = new PageInfo(list);

        return page;
    }


    public PageInfo<Zagents> getZagentsInfoPage(Zagents info, Integer pageNum, Integer pageSize) throws Exception {

        PageHelper.startPage(pageNum.intValue(), pageSize.intValue());

        List<Zagents> list = this.zagentsMapper.selectByParms(info);

        PageInfo<Zagents> page = new PageInfo(list);

        return page;
    }

    public Map<String, Object> getZsaleCount() {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        map.put("trial", Long.valueOf(this.zsaleMapper.getZsaleCount(Long.valueOf(1L))));

        map.put("lease", Long.valueOf(this.zsaleMapper.getZsaleCount(Long.valueOf(2L))));

        map.put("buyOut", Long.valueOf(this.zsaleMapper.getZsaleCount(Long.valueOf(3L))));

        return map;
    }

    public Map<String, Object> addZsale(Zsale info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getSn() == null) {

            return CommonUtil.resultMsg("FAIL", "sn不能为空");
        }


        if (!StringUtils.isEmpty(info.getTrialStartDateStr())) {

            info.setTrialStartDate(DateUtils.CSTToDate(info.getTrialStartDateStr()));

            info.setQueryDate(DateUtils.CSTToDate(info.getTrialStartDateStr()));
        }


        if (!StringUtils.isEmpty(info.getTrialEndDateStr())) {

            info.setTrialEndDate(DateUtils.CSTToDate(info.getTrialEndDateStr()));
        }


        Robot robot = this.robotMapper.getRobotSn(info.getSn());

        if (robot == null) {

            return CommonUtil.resultMsg("FAIL", "sn错误,没找到对应的机器人id");
        }


        Zagents zg = this.zagentsMapper.selectBySn(info.getSn());

        if (zg != null) {

            return CommonUtil.resultMsg("FAIL", "sn已经在代理商存在!");
        }


        info.setRobotId(robot.getRobotId());

        Zrobot zrobot = this.zrobotMapper.selectByRobotId(robot.getRobotId());

        if (zrobot == null) {

            Zrobot record = new Zrobot();

            record.setRobotId(robot.getRobotId());

            this.zrobotMapper.insertSelective(record);
        }

        Zsale zsale = this.zsaleMapper.selectByRobotId(robot.getRobotId());

        if (zsale != null) {

            return CommonUtil.resultMsg("FAIL", "机器人id数据已经存在！");
        }

        SchoolRobot sr = this.schoolRobotMapper.selectRobotById(robot.getRobotId());

        if (sr != null) {

            info.setSchoolId(sr.getSchoolId());
        }

        this.zsaleMapper.insertSelective(info);

        return map;
    }

    public Map<String, Object> addTrialRobot(Zsale info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getSn() == null) {

            return CommonUtil.resultMsg("FAIL", "sn不能为空");
        }


        Zagents zg = this.zagentsMapper.selectBySn(info.getSn());

        if (zg != null) {

            return CommonUtil.resultMsg("FAIL", "sn已经在代理商存在!");
        }


        Robot robot = this.robotMapper.getRobotSn(info.getSn());

        if (robot == null) {

            return CommonUtil.resultMsg("FAIL", "sn错误,没找到对应的机器人id");
        }


        info.setRobotId(robot.getRobotId());


        SchoolRobot sr = this.schoolRobotMapper.selectRobotById(robot.getRobotId());


        if (sr == null) {

            return CommonUtil.resultMsg("FAIL", "机器人未绑定幼儿园,先绑定在添加sn!");
        }


        if (sr.getStatus().intValue() == 0) {

            return CommonUtil.resultMsg("FAIL", "机器人未绑定幼儿园,先绑定在添加sn!");
        }


        Zsale zsale = this.zsaleMapper.selectByRobotId(robot.getRobotId());

        if (zsale != null) {

            return CommonUtil.resultMsg("FAIL", "机器人id数据已经存在！");
        }


        Zrobot zrobot = this.zrobotMapper.selectByRobotId(robot.getRobotId());

        if (zrobot == null) {

            Zrobot record = new Zrobot();

            record.setRobotId(robot.getRobotId());

            this.zrobotMapper.insertSelective(record);
        }


        info.setTrialStartDate(sr.getCreateTime());

        info.setTrialEndDate(DateUtils.optDate(sr.getCreateTime(), 14));

        info.setType(Integer.valueOf(1));

        info.setQueryDate(sr.getCreateTime());

        Member member = this.memberMapper.selectByPhone(info.getSalesPhone());

        if (member == null) {

            return CommonUtil.resultMsg("FAIL", "数据有误");
        }

        info.setSalesManager(member.getName());

        info.setDepartment(member.getDepartment());

        info.setSchoolId(sr.getSchoolId());

        this.zsaleMapper.insertSelective(info);

        return map;
    }

    public Map<String, Object> addAgentRobot(Zagents info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getSn() == null) {

            return CommonUtil.resultMsg("FAIL", "sn不能为空");
        }


        Robot robot = this.robotMapper.getRobotSn(info.getSn());

        if (robot == null) {

            return CommonUtil.resultMsg("FAIL", "sn错误,没找到对应的机器人id");
        }


        Zagents zg = this.zagentsMapper.selectBySn(info.getSn());

        if (zg != null) {

            return CommonUtil.resultMsg("FAIL", "sn已经在代理商存在!");
        }


        Zsale zsale = this.zsaleMapper.selectByRobotId(robot.getRobotId());

        if (zsale != null) {

            return CommonUtil.resultMsg("FAIL", "机器人id数据已经存在！");
        }


        Zrobot zrobot = this.zrobotMapper.selectByRobotId(robot.getRobotId());

        if (zrobot == null) {

            Zrobot record = new Zrobot();

            record.setRobotId(robot.getRobotId());

            this.zrobotMapper.insertSelective(record);
        }


        info.setRobotId(robot.getRobotId());

        this.zagentsMapper.insertSelective(info);

        return map;
    }

    public Map<String, Object> editZsale(Zsale info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        int flag = 0;

        if (!StringUtils.isEmpty(info.getTrialStartDateStr())) {

            info.setTrialStartDate(DateUtils.CSTToDate(info.getTrialStartDateStr()));

            info.setQueryDate(DateUtils.CSTToDate(info.getTrialStartDateStr()));
        }


        info.setTrialEndDate(DateUtils.CSTToDate(info.getTrialEndDateStr()));

        if (!StringUtils.isEmpty(info.getTrialEndDateStr()) && info.getTrialEndDate().after(new Date())) {

            flag = 1;
        }


        if (!StringUtils.isEmpty(info.getLeaseStartDateStr())) {

            info.setLeaseStartDate(DateUtils.CSTToDate(info.getLeaseStartDateStr()));

            info.setQueryDate(DateUtils.CSTToDate(info.getLeaseStartDateStr()));
        }


        info.setLeaseEndDate(DateUtils.CSTToDate(info.getLeaseEndDateStr()));

        if (!StringUtils.isEmpty(info.getLeaseEndDateStr()) && info.getLeaseEndDate().after(new Date())) {

            flag = 1;
        }


        if (!StringUtils.isEmpty(info.getBuyOutDateStartStr())) {

            info.setBuyOutDate(DateUtils.CSTToDate(info.getBuyOutDateStartStr()));

            info.setQueryDate(DateUtils.CSTToDate(info.getBuyOutDateStartStr()));
        }


        info.setSoftwareRenewDate(DateUtils.CSTToDate(info.getSoftwareRenewDateStr()));


        if (!StringUtils.isEmpty(info.getSoftwareRenewDateStr()) && info.getSoftwareRenewDate().after(new Date())) {

            flag = 1;
        }


        info.setEditStatus(Integer.valueOf(1));

        Zsale zs = this.zsaleMapper.selectByPrimaryKey(info.getId());

        if (zs.getEditStatus().intValue() == 2) {

            info.setEditStatus(Integer.valueOf(3));
        }

        if (zs.getQueryDate() != null) {

            info.setQueryDate(zs.getQueryDate());
        }

        if (flag == 1) {


            Robot r = new Robot();

            r.setRobotId(zs.getRobotId());

            r.setChannel("CH10000");

            this.robotMapper.edit(r);

            Zrobot zrobot = new Zrobot();

            zrobot.setRobotId(zs.getRobotId());

            zrobot.setLockStatus(Integer.valueOf(1));

            this.zrobotMapper.updateByPrimaryKeySelective(zrobot);
        }


        this.zsaleMapper.updateByPrimaryKeySelective(info);

        return map;
    }

    public Map<String, Object> deletZsale(Zsale info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getId() == null) {

            return CommonUtil.resultMsg("FAIL", "id数据错误");
        }

        Zsale zsa = this.zsaleMapper.selectByPrimaryKey(info.getId());


        Robot r = new Robot();

        r.setRobotId(zsa.getRobotId());

        r.setChannel("CH10000");

        this.robotMapper.edit(r);


        Zrobot record = new Zrobot();

        record.setLockStatus(Integer.valueOf(1));

        record.setBanClassStatus(Integer.valueOf(1));

        record.setRobotId(zsa.getRobotId());

        this.zrobotMapper.updateByPrimaryKeySelective(record);


        this.zsaleMapper.deleteByPrimaryKey(info.getId());

        return map;
    }

    public List<LProvince> selectByLProvince(LProvince lProvince) {

        return this.lProvinceMapper.selectByLProvince(lProvince);
    }

    public Map<String, Object> deletYorderDetails(YorderDetails info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        YorderDetails yo = this.yorderDetailsMapper.selectByPrimaryKey(info.getId());

        this.yorderDetailsMapper.deleteByPrimaryKey(info.getId());

        this.yorderDetailsMapper.updateByOrderNumber(info.getOrderNo());

        return map;
    }

    public Map<String, Object> deletYpaidDetails(YpaidDetails info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
        YpaidDetails deleteYpaidDetails = ypaidDetailsMapper.selectByPrimaryKey(info.getId());

        AuditLog log = new AuditLog();
        log.setOperationType(DbOperation.DELETE.getName());
        log.setOldData(JSONObject.toJSONString(deleteYpaidDetails));
        log.setRecordId(deleteYpaidDetails.getId());
        log.setTableName("y_paid_details");
        log.setUser(info.getExp2());
        logMapper.insertAuditLog(log);
        this.ypaidDetailsMapper.deleteByPrimaryKey(info.getId());
        return map;
    }

    public Map<String, Object> deletZagents(Zagents info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getId() == null) {

            return CommonUtil.resultMsg("FAIL", "id数据错误");
        }


        Zagents zsa = this.zagentsMapper.selectByPrimaryKey(info.getId());

        Robot r = new Robot();

        r.setRobotId(zsa.getRobotId());

        r.setChannel("CH10000");

        this.robotMapper.edit(r);


        Zrobot record = new Zrobot();

        record.setLockStatus(Integer.valueOf(1));

        record.setBanClassStatus(Integer.valueOf(1));

        record.setRobotId(zsa.getRobotId());

        this.zrobotMapper.updateByPrimaryKeySelective(record);


        this.zagentsMapper.deleteByPrimaryKey(info.getId());

        return map;
    }

    public Map<String, Object> addZagents(Zagents info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getSn() == null) {

            return CommonUtil.resultMsg("FAIL", "sn不能为空");
        }


        Robot robot = this.robotMapper.getRobotSn(info.getSn());

        if (robot == null) {

            return CommonUtil.resultMsg("FAIL", "sn错误,没找到对应的机器人id");
        }


        Zsale zs = this.zsaleMapper.selectBySn(info.getSn());

        if (zs != null) {

            return CommonUtil.resultMsg("FAIL", "sn已经在直营存在!");
        }


        info.setRobotId(robot.getRobotId());

        Zrobot zrobot = this.zrobotMapper.selectByRobotId(robot.getRobotId());

        if (zrobot == null) {

            Zrobot record = new Zrobot();

            record.setRobotId(robot.getRobotId());

            this.zrobotMapper.insertSelective(record);
        }

        Zagents zagents = this.zagentsMapper.selectByRobotId(robot.getRobotId());

        if (zagents != null) {

            return CommonUtil.resultMsg("FAIL", "机器人id数据已经存在！");
        }

        this.zagentsMapper.insertSelective(info);

        return map;
    }

    public Map<String, Object> editZagents(Zagents info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (info.getSn() == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        this.zagentsMapper.updateByPrimaryKeySelective(info);

        return map;
    }

    public Map<String, Object> editYorderDetails(YorderDetails info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());


        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        if (!StringUtils.isEmpty(info.getShippingDateStr())) {

            info.setShippingDate(DateUtils.CSTToDate(info.getShippingDateStr()));
        }


        if (!StringUtils.isEmpty(info.getProductName())) {

            Ycost ycost = this.ycostMapper.selectByProduct(info.getProductName());

            if (ycost != null) {

                info.setProductCost(ycost.getPrice().multiply(new BigDecimal(info.getNum().intValue())));
            }
        }


        this.yorderDetailsMapper.updateByPrimaryKeySelective(info);

        return map;
    }

    public Map<String, Object> editYpaidDetails(YpaidDetails info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
        if (info == null) {
            return CommonUtil.resultMsg("FAIL", "数据错误");
        }
        if (!StringUtils.isEmpty(info.getCollectionDateStr())) {
            info.setCollectionDate(DateUtils.CSTToDate(info.getCollectionDateStr(), DateUtils.DATE_PATTERN_SimpleDateFormat));
        }
        YpaidDetails oldYpaidDetails = ypaidDetailsMapper.selectByPrimaryKey(info.getId());
        if ("1".equals(oldYpaidDetails.getDataSystem())) {
            return CommonUtil.resultMsg("FAIL", "老数据，不可以修改");
        }

        // 计算销售分成金额 = 收款金额 * 销售分成比例
        if (info.getAmount() != null && info.getSalesCommissionRatio() != null) {
            BigDecimal salesCommissionAmount = info.getAmount().multiply(info.getSalesCommissionRatio()).setScale(2, RoundingMode.HALF_UP);
            info.setSalesCommissionAmount(salesCommissionAmount);
        }

        // 计算电话分成金额 = 收款金额 * 电话分成比例
        if (info.getAmount() != null && info.getPhoneCommissionRatio() != null) {
            BigDecimal phoneCommissionAmount = info.getAmount().multiply(info.getPhoneCommissionRatio()).setScale(2, RoundingMode.HALF_UP);
            info.setPhoneCommissionAmount(phoneCommissionAmount);
        }

        // 计算总分成金额 = 销售分成金额 + 电话分成金额
        BigDecimal salesAmount = info.getSalesCommissionAmount() != null ? info.getSalesCommissionAmount() : BigDecimal.ZERO;
        BigDecimal phoneAmount = info.getPhoneCommissionAmount() != null ? info.getPhoneCommissionAmount() : BigDecimal.ZERO;
        info.setTotalCommissionAmount(salesAmount.add(phoneAmount).setScale(2, RoundingMode.HALF_UP));

        AuditLog log = new AuditLog();
        log.setOperationType(DbOperation.UPDATE.getName());
        log.setOldData(JSONObject.toJSONString(oldYpaidDetails));
        log.setNewData(JSONObject.toJSONString(info));
        log.setRecordId(oldYpaidDetails.getId());
        log.setTableName("y_paid_details");
        log.setUser(info.getExp1());
        logMapper.insertAuditLog(log);

        this.ypaidDetailsMapper.updateByPrimaryKeySelective(info);
        return map;
    }

    public Map<String, Object> monthDataEntrys(YdepartProfit info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        String mounthDateStr = info.getMonth();

        if (StringUtils.isEmpty(mounthDateStr)) {

            mounthDateStr = DateUtils.format(new Date(), "yyyy-MM");
        }
        log.info("====ycostMapper.listByProduct:");
        List<Ycost> costList = ycostMapper.listByProduct(Arrays.asList("人均办公租金杂费公摊", "培训服务占总销售比例", "基本税费率", "货物损耗率"));
        log.info("====ycostMapper.listByProduct:--end");
        Map<String, Ycost> productCostMap = new HashMap<>();
        for (Ycost ycost : costList) {
            productCostMap.put(ycost.getProduct(), ycost);
        }
        int rentPerFee = productCostMap.get("人均办公租金杂费公摊").getPrice().intValue();
        BigDecimal trainProportion = productCostMap.get("培训服务占总销售比例").getPrice();
        BigDecimal basicTaxRate = productCostMap.get("基本税费率").getPrice();
        BigDecimal lossRate = productCostMap.get("货物损耗率").getPrice();

        List<YorderDetailsPrice> priceList = yorderDetailsMapper.selectProductPriceByMonth(mounthDateStr);
        List<YorderDetails> yorderDetailsList = new ArrayList<>();
        for (YorderDetailsPrice detailsPrice : priceList) {
            BigDecimal price = detailsPrice.getPrice();
            if (price != null) {
                YorderDetails yorderDetails = new YorderDetails();
                yorderDetails.setId(detailsPrice.getId());
                yorderDetails.setProductCost(price.multiply(new BigDecimal(detailsPrice.getNum())));
                yorderDetailsList.add(yorderDetails);
            }
        }
        if (!ObjectUtils.isEmpty(yorderDetailsList)) {
            log.info("====yorderDetailsMapper.batchUpdateProductCost:");
            yorderDetailsMapper.batchUpdateProductCost(yorderDetailsList);
        }

        YorderDetails yod = this.yorderDetailsMapper.getTaxPriceAndProductCostByMonthAndDepart(mounthDateStr, info.getDepartment());
        try {
            int salesVolume = 0;
            int grossProfit = 0;
            if (yod != null) {
                salesVolume = yod.getTaxPrice().multiply(new BigDecimal(1)).setScale(0, 4).intValue();
                grossProfit = yod.getTaxPrice().subtract(yod.getProductCost()).multiply(new BigDecimal(1)).setScale(0, 4).intValue();
            }
            info.setGrossProfit(Integer.valueOf(grossProfit));
            info.setSalesVolume(Integer.valueOf(salesVolume));
            info.setMonth(mounthDateStr);
            Integer advertSales = info.getAdvertSales() == null ? 0 : info.getAdvertSales();
            if (salesVolume != 0) {
                info.setAdvertSaleProportion((new BigDecimal(advertSales.intValue())).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setAdvertSaleProportion(new BigDecimal(0));
            }
            info.setNoAdvertSales(Integer.valueOf(salesVolume - advertSales.intValue()));
            Integer noAdvertSales = info.getNoAdvertSales() == null ? 0 : info.getNoAdvertSales();
            if (salesVolume != 0) {
                info.setNoAdvertSaleProportion((new BigDecimal(noAdvertSales.intValue())).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setNoAdvertSaleProportion(new BigDecimal(0));
            }
            Integer advertFee = info.getAdvertFee() == null ? 0 : info.getAdvertFee();
            if (advertFee.intValue() == 0) {
                info.setAdvertFeeProportion(new BigDecimal(0.0D));
            } else {

                info.setAdvertFeeProportion((new BigDecimal(salesVolume - noAdvertSales.intValue())).divide(new BigDecimal(advertFee.intValue()), 1, 4));
            }
            Integer commissionAmount = info.getCommissionAmount() == null ? 0 : info.getCommissionAmount();
            if (salesVolume != 0) {
                info.setCommissionProportion((new BigDecimal(commissionAmount.intValue())).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {

                info.setCommissionProportion(new BigDecimal(0));
            }
            Integer totalBaseSalary = info.getTotalBaseSalary() == null ? 0 : info.getTotalBaseSalary();
            Integer monthIncome = info.getMonthIncome() == null ? 0 : info.getMonthIncome();
            info.setMonthIncome(commissionAmount + totalBaseSalary);
            Integer travelFee = info.getTravelFee() == null ? 0 : info.getTravelFee();
            Integer peopleNum = info.getPeopleNum() == null ? 0 : info.getPeopleNum();
            if (peopleNum > 0) {
                info.setPerCapitaSales((new BigDecimal(salesVolume)).divide(new BigDecimal(peopleNum), 0, 4).intValue());
                info.setPerMonthIncome((new BigDecimal(monthIncome)).divide(new BigDecimal(peopleNum), 0, 4).intValue());
                info.setTravelPerFee((new BigDecimal(travelFee)).divide(new BigDecimal(peopleNum), 0, 4).intValue());
            }

            info.setRentPerFee(rentPerFee);
            Integer freightAmount = info.getFreightAmount() == null ? 0 : info.getFreightAmount();
            if (salesVolume != 0) {
                info.setFreightProportion((new BigDecimal(freightAmount)).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setFreightProportion(new BigDecimal(0));
            }
            info.setTrainProportion(trainProportion);
            info.setBasicTaxRate(basicTaxRate);
            Integer rebateAmount = info.getRebateAmount() == null ? 0 : info.getRebateAmount();
            if (salesVolume != 0) {
                info.setRebateProportion((new BigDecimal(rebateAmount)).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setRebateProportion(new BigDecimal(0));
            }

            info.setLossRate(lossRate);
            Integer incidentalAmount = info.getIncidentalAmount() == null ? 0 : info.getIncidentalAmount();
            if (salesVolume != 0) {
                info.setIncidentalProportion((new BigDecimal(incidentalAmount)).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setIncidentalProportion(new BigDecimal(0));
            }
            BigDecimal bigDecimal = info.getTrainProportion() == null ? new BigDecimal("0") : info.getTrainProportion();
            info.setTrainAmount(Integer.valueOf((new BigDecimal(salesVolume)).multiply(bigDecimal).divide(new BigDecimal(100), 0, 4).intValue()));
            BigDecimal infoBasicTaxRate = info.getBasicTaxRate() == null ? new BigDecimal("0") : info.getBasicTaxRate();
            info.setTaxRate(Integer.valueOf((new BigDecimal(salesVolume)).multiply(infoBasicTaxRate).divide(new BigDecimal(100), 0, 4).intValue()));

            BigDecimal infoLossRate = info.getLossRate() == null ? new BigDecimal("0") : info.getLossRate();
            info.setLossAmount(Integer.valueOf((new BigDecimal(salesVolume)).multiply(infoLossRate).divide(new BigDecimal(100), 0, 4).intValue()));

            if (salesVolume != 0) {
                info.setGrossMargin((new BigDecimal(grossProfit)).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setGrossMargin(new BigDecimal(0));
            }
            Integer infoRentPerFee = info.getRentPerFee() == null ? 0 : info.getRentPerFee();
            Integer infoTaxRate = info.getTaxRate() == null ? 0 : info.getTaxRate();
            Integer trainAmount = info.getTrainAmount() == null ? 0 : info.getTrainAmount();
            Integer lossAmount = info.getLossAmount() == null ? 0 : info.getLossAmount();
            info.setTotalCost(advertFee + totalBaseSalary + infoRentPerFee * peopleNum + infoTaxRate + trainAmount + freightAmount + commissionAmount + travelFee + rebateAmount + lossAmount + incidentalAmount);

            Integer infoGrossProfit = info.getGrossProfit() == null ? 0 : info.getGrossProfit();
            Integer totalCost = info.getTotalCost() == null ? 0 : info.getTotalCost();
            info.setNetProfit(infoGrossProfit - totalCost);

            BigDecimal operateFee = info.getOperateFee() == null ? new BigDecimal("0") : info.getOperateFee();
            info.setDepartmentProportion((new BigDecimal(monthIncome)).multiply(operateFee).divide(new BigDecimal(1), 0, 4).intValue());
            Integer netProfit = info.getNetProfit() == null ? 0 : info.getNetProfit();
            if (salesVolume != 0) {
                info.setNetProfitMargin((new BigDecimal(netProfit)).divide(new BigDecimal(salesVolume), 3, 4).multiply(new BigDecimal(100)));
            } else {
                info.setNetProfitMargin(new BigDecimal(0));
            }
            Integer departmentProportion = info.getDepartmentProportion() == null ? 0 : info.getDepartmentProportion();
            info.setNetRealProfit(netProfit - departmentProportion);
        } catch (Throwable e) {
            log.info("monthDataEntrys处理数据异常：", e);
            return CommonUtil.resultMsg(ResultMessage.FAIL.getKey(), ResultMessage.FAIL.getValue());
        }

        YdepartProfit yd = this.ydepartProfitMapper.selectByDepartAndMonth(info.getDepartment(), mounthDateStr);
        if (yd != null) {
            info.setId(yd.getId());
            this.ydepartProfitMapper.updateByPrimaryKeySelective(info);
        } else {
            this.ydepartProfitMapper.insertSelective(info);
        }
        return map;
    }

    public Map<String, Object> getMonthDataEntrys(YdepartProfit info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        String mounthDateStr = info.getMonth();

        if (StringUtils.isEmpty(mounthDateStr)) {

            mounthDateStr = DateUtils.format(new Date(), "yyyy-MM");
        }

        YdepartProfit yd = this.ydepartProfitMapper.selectByDepartAndMonth(info.getDepartment(), mounthDateStr);

        map.put("data", yd);

        return map;
    }

    public Map<String, Object> copyYpaidDetails(@RequestBody JSONObject jo) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        Long id = jo.getLong("id");

        YorderDetails yo = this.yorderDetailsMapper.selectByPrimaryKey(id);

        yo.setId(null);

        yo.setUniqueCode(String.valueOf(yo.getUniqueCode()) + "_copy");

        yo.setIfSynchro("0");

        this.yorderDetailsMapper.insertSelective(yo);

        return map;
    }


    public Map<String, Object> synchroYpaidDetails(@RequestBody JSONObject jo) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        String ids = jo.getString("ids");

        String[] id = ids.split(",");

        String orderTypes = "";
        byte b;
        int i;
        String[] arrayOfString1;

        for (i = (arrayOfString1 = id).length, b = 0; b < i; ) {
            String idl = arrayOfString1[b];

            YorderDetails yo = this.yorderDetailsMapper.selectByPrimaryKey(Long.valueOf(idl));
            if ("服务费,退货, 续租".contains(yo.getOrderType())) {
                orderTypes = String.valueOf(orderTypes) + yo.getOrderType();
            } else {
                LLogisticsManager lg = new LLogisticsManager();
                lg.setExp38(yo.getUniqueCode());
                List<LLogisticsManager> list = this.lLogisticsManagerMapper.selectByParms(lg);
                lg.setExp6(new Date());
                lg.setSystemManageOrder(yo.getUniqueCode());

                lg.setOrderDate(yo.getShippingDate());

                lg.setExp39(yo.getDepartment());

                lg.setExp40(yo.getSalesman());

                lg.setPlatformOrder(yo.getOrderNo());

                lg.setClientProperty(yo.getClientProperty());

                lg.setOrderType(yo.getOrderType());

                lg.setCustomerNo(yo.getShippingCompanyCode());

                lg.setCustomerName(yo.getCompany());

                lg.setProductCode(yo.getProductCode());

                lg.setProductName(yo.getProductName().replace("-租赁", ""));


                lg.setAccountingUnit(yo.getPackagingUnit());

                lg.setSendGoodsNum(yo.getNum());

                lg.setUnitPrice(yo.getTaxUnitPrice());

                lg.setPrice(yo.getTaxPrice());

                lg.setPaymentStatus(yo.getPaymentStatus());

                lg.setDeposit(yo.getPayPrice());

                lg.setConsigneeName1(yo.getConsignee1());

                lg.setConsigneeName2(yo.getConsignee2());

                lg.setConsigneephone1(yo.getConsigneePhone1());

                lg.setConsigneephone2(yo.getConsigneePhone2());

                lg.setAddressDetail(yo.getConsigneeAddress());

                lg.setExp15(yo.getOrderDemand());

                lg.setRemarks(yo.getRemark());

                lg.setCardGoods(yo.getCard());

                lg.setYnPaymentReceived(yo.getCollectionOrNot());

                if (!StringUtils.isEmpty(yo.getCollectionAmount())) {

                    lg.setCollectMoney(new BigDecimal(yo.getCollectionAmount()));
                }

                lg.setIfInvoice(yo.getInvoiceOrNot());

                lg.setExp13(yo.getInvoiceType());

                lg.setExp3(yo.getInvoiceHeader());


                lg.setSendGoodsOrder(yo.getShipmentNoticeNo());


                lg.setSystemOrder(yo.getOrdertNo());

                lg.setSendGoodsType(yo.getShipmentReturn());


                if (!StringUtils.isEmpty(yo.getRobotOrNot())) {
                    if (yo.getRobotOrNot().trim().equals("是")) {
                        lg.setExp7("机器人");
                    } else if ("小胖口算机器人S198黑金、小胖口算机器人S198蓝色".contains(yo.getProductName())) {
                        lg.setExp7("S198");
                    } else if ("小胖机器人S99粉色、小胖机器人S99黑色、小胖机器人S99蓝色、小胖机器人S99红色".contains(yo.getProductName())) {
                        lg.setExp7("S99");
                    } else {
                        lg.setExp7("其它");
                    }
                } else {
                    lg.setExp7("其它");
                }

                try {
                    checkLLogisticsManager(lg);
                } catch (Exception e) {
                    e.printStackTrace();
                }


                if (list.size() != 0) {
                    LLogisticsManager lg2 = new LLogisticsManager();
                    lg2.setSystemOrder(yo.getOrdertNo());
                    lg2.setExp38(yo.getUniqueCode());
                    log.info("update_order:" + yo.getOrdertNo() + "," + yo.getUniqueCode());
                    this.lLogisticsManagerMapper.updateByUnique(lg2);
                } else {
                    lg.setStatus(Integer.valueOf(1));
                    this.lLogisticsManagerMapper.insertSelective(lg);
                }
                yo.setIfSynchro("1");
                this.yorderDetailsMapper.updateByPrimaryKeySelective(yo);
            }
            b++;
        }

        map.put("orderTpye", orderTypes);

        return map;
    }

    public Map<String, Object> checkLLogisticsManager(LLogisticsManager logisticsManager) throws Exception {

        if (logisticsManager.getOrderDate() == null) {
            return CommonUtil.resultMsg("FAIL", "订单日期不能为空");
        }

        String adress = logisticsManager.getAddressDetail();

        String cur = adress.trim().substring(0, 2);

        System.out.println(cur);


        String province = "";


        String cityName = "";

        for (String temp : DataCacheUtil.PROVINCESLIST) {
            if (temp.contains(cur)) {
                province = temp;
                break;
            }
        }

        if ("".equals(province)) {

            for (String city : DataCacheUtil.CITYLIST) {
                if (adress.contains(city)) {
                    cityName = city;
                    province = (String) DataCacheUtil.CITYPROVINCEMAP.get(city);
                    break;
                }
            }
            if ("".equals(province)) {
                System.out.println("省名称错误");
                return CommonUtil.resultMsg("SUCCESS", "物流校驗成功");
            }
        } else {

            List<String> citys = (List<String>) DataCacheUtil.PROVINCECITYMAP.get(province);

            if (citys == null || citys.size() == 0) {

                return CommonUtil.resultMsg("SUCCESS", "物流校驗成功");
            }

            for (String city : citys) {

                String city2 = city.trim().substring(0, 2);
                if (adress.contains(city2)) {
                    cityName = city;
                }
            }
        }

        if (StringUtils.isEmpty(logisticsManager.getReceivingProvince())) {

            logisticsManager.setReceivingProvince(province);
        }

        if (StringUtils.isEmpty(logisticsManager.getReceivingCityCounty())) {

            logisticsManager.setReceivingCityCounty(cityName);
        }

        if (StringUtils.isEmpty(logisticsManager.getCityLevel())) {

            logisticsManager.setCityLevel((String) DataCacheUtil.CITYLEVELMAP.get(cityName));
        }


        return CommonUtil.resultMsg("SUCCESS", "物流校驗成功");
    }

    public Map<String, Object> updateRobotStatus(Zrobot info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }

        if (StringUtils.isEmpty(info.getRobotId())) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }

        if (info.getLockStatus() != null) {

            if (info.getLockStatus().intValue() == 1) {

                Robot r = new Robot();

                r.setRobotId(info.getRobotId());

                r.setChannel("CH10000");

                this.robotMapper.edit(r);

            } else if (info.getLockStatus().intValue() == 0) {

                Robot r = new Robot();

                r.setRobotId(info.getRobotId());

                r.setChannel("CH10010");

                this.robotMapper.edit(r);
            }
        }

        this.zrobotMapper.updateByPrimaryKeySelective(info);

        return map;
    }


    public Map<String, Object> updateQuota(Student info) {
        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
        if (info == null) {
            return CommonUtil.resultMsg("FAIL", "数据错误");
        }
        if (!StringUtils.isEmpty(info.getQuotaDateStr())) {
            info.setQuotaDate(DateUtils.CSTToDate(info.getQuotaDateStr()));
        }


        int quotaNum = 0;
        if (info.getQuota() == 1) {
            quotaNum = info.getQuotaNum();
        } else if (info.getQuota() == 2) {
            if (com.efrobot.robotstore.util.StringUtils.compareDateBoolen(info.getQuotaDate(), new Date())) {
                quotaNum = info.getQuotaNum();
            }
        }
        Integer schoolType = info.getSchoolType();
        SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.find(schoolType);
        int gradeLimit = schoolTypeEnum.equals(SchoolTypeEnum.kindergarten) ? 5 : 7;
        int childNum = studentMapper.selectCountBySchoolId(info.getId(), gradeLimit);
        int robotNum = studentMapper.selectRobotCountBySchoolId(info.getId());
        int oneRobotNum = 50000;
        int surplusNum = oneRobotNum * robotNum + quotaNum - childNum;
        info.setSurplusNum(surplusNum);
        info.setChildTotal(childNum);

        studentMapper.updateByPrimaryKeySelective(info);

        return map;
    }

    public Map<String, Object> nextRemind(Zsale info) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        if (info == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }

        if (info.getId() == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        Zsale zs = this.zsaleMapper.selectByPrimaryKey(info.getId());

        if (zs == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }

        if (zs.getSoftwareRenewDate() == null) {

            return CommonUtil.resultMsg("FAIL", "数据错误");
        }


        Calendar calendar = Calendar.getInstance();

        calendar.setTime(zs.getSoftwareRenewDate());

        calendar.add(1, 1);

        zs.setSoftwareRenewDate(calendar.getTime());

        this.zsaleMapper.updateByPrimaryKey(zs);


        Robot r = new Robot();

        r.setRobotId(zs.getRobotId());

        r.setChannel("CH10000");

        this.robotMapper.edit(r);

        Zrobot zrobot = new Zrobot();

        zrobot.setRobotId(zs.getRobotId());

        zrobot.setLockStatus(Integer.valueOf(1));

        this.zrobotMapper.updateByPrimaryKeySelective(zrobot);

        return map;
    }


    public Map<String, Object> importZagentsExcel(List<String[]> list) {

        List<String> failList = new ArrayList<>();

        List<String> repeatList = new ArrayList<>();

        int succNum = 0;

        for (String[] arrs : list) {

            Zagents zagents = new Zagents();
            try {

                if (arrs[0].equals("")) {
                    break;
                }

                zagents.setSn(arrs[0]);

                zagents.setName(arrs[1]);

                zagents.setPhone(arrs[2]);

                zagents.setAdress(arrs[3]);


                Zsale zs = this.zsaleMapper.selectBySn(arrs[0]);

                if (zs != null) {
                    continue;
                }

                Zagents z = this.zagentsMapper.selectBySn(arrs[0]);

                if (z != null) {

                    zagents.setId(z.getId());

                    this.zagentsMapper.updateByPrimaryKeySelective(zagents);
                } else {

                    Robot robot = this.robotMapper.getRobotSn(arrs[0]);

                    if (robot == null) {

                        failList.add("机器人SN码:" + arrs[0]);
                        continue;
                    }

                    zagents.setRobotId(robot.getRobotId());

                    Zrobot zrobot = this.zrobotMapper.selectByRobotId(robot.getRobotId());

                    if (zrobot == null) {

                        Zrobot record = new Zrobot();

                        record.setRobotId(robot.getRobotId());

                        this.zrobotMapper.insertSelective(record);
                    }

                    this.zagentsMapper.insertSelective(zagents);
                }

                succNum++;

            } catch (Exception e) {

                failList.add("机器人SN码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));

        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }

    public Map<String, Object> importContractExcel(List<String[]> list) {

        List<String> failList = new ArrayList<>();

        int succNum = 0;

        for (String[] arrs : list) {

            Ycontract ycontract = new Ycontract();
            try {

                if (arrs[0].equals("")) {
                    break;
                }

                ycontract.setUniqueCode(arrs[0]);

                ycontract.setCustomerCode(arrs[2]);

                ycontract.setCustomer(arrs[3]);


                if (!StringUtils.isEmpty(arrs[10])) {

                    ycontract.setStartDate(DateUtils.CSTToDate(arrs[10]));
                }

                if (!StringUtils.isEmpty(arrs[11])) {

                    ycontract.setEndDate(DateUtils.CSTToDate(arrs[11]));
                }


                ycontract.setDepartment(arrs[16]);

                ycontract.setSalesman(arrs[17]);

                ycontract.setContacts(arrs[23]);

                ycontract.setPhone(arrs[24]);

                ycontract.setIfSign(arrs[25]);

                ycontract.setIfSendBack(arrs[26]);

                ycontract.setProtocolType(arrs[31]);

                ycontract.setFilePdf(arrs[32]);


                Ycontract z = this.ycontractMapper.selectByUniqueCode(arrs[0]);

                if (z != null) {

                    if (!StringUtils.isEmpty(arrs[27]) && (
                            arrs[27].equals("已退贷") || arrs[27].equals("无效"))) {

                        this.ycontractMapper.deleteByPrimaryKey(z.getId());

                        continue;
                    }

                    ycontract.setId(z.getId());

                    this.ycontractMapper.updateByPrimaryKeySelective(ycontract);
                } else {

                    if (!StringUtils.isEmpty(arrs[27]) && (
                            arrs[27].equals("已退贷") || arrs[27].equals("无效"))) {
                        continue;
                    }


                    this.ycontractMapper.insertSelective(ycontract);
                }

                succNum++;

            } catch (Exception e) {

                failList.add("唯一编码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));

        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }


    public Map<String, Object> importInvoiceExcel(List<String[]> list) {

        List<String> failList = new ArrayList<>();

        int succNum = 0;

        for (String[] arrs : list) {

            Yinvoice yinvoice = new Yinvoice();
            try {

                if (arrs[0].equals("")) {
                    break;
                }

                yinvoice.setUniqueCode(arrs[0]);

                yinvoice.setType(arrs[1]);

                if (!StringUtils.isEmpty(arrs[2])) {

                    yinvoice.setBillingDate(DateUtils.CSTToDate(arrs[2]));
                }

                yinvoice.setPrice(arrs[3]);

                yinvoice.setOrderNo(arrs[4]);

                yinvoice.setMail(arrs[5]);

                if (!StringUtils.isEmpty(arrs[6])) {

                    yinvoice.setSendDate(DateUtils.CSTToDate(arrs[6]));
                }

                yinvoice.setRecipient(arrs[7]);


                if (!StringUtils.isEmpty(arrs[8])) {

                    yinvoice.setRecipientDate(DateUtils.CSTToDate(arrs[8]));
                }

                yinvoice.setExp1(arrs[9]);

                Yinvoice z = this.yinvoiceMapper.selectByUniqueCode(arrs[0]);

                if (z != null) {

                    yinvoice.setId(z.getId());

                    this.yinvoiceMapper.updateByPrimaryKeySelective(yinvoice);
                } else {

                    this.yinvoiceMapper.insertSelective(yinvoice);
                }

                succNum++;

            } catch (Exception e) {

                failList.add("唯一编码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));

        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }

    public Map<String, Object> importBondExcel(List<String[]> list) {

        List<String> failList = new ArrayList<>();

        int succNum = 0;

        for (String[] arrs : list) {

            Ybond ybond = new Ybond();
            try {

                if (arrs[0].equals("")) {
                    break;
                }

                ybond.setUniqueCode(arrs[0]);

                ybond.setCustomer(arrs[1]);

                if (!StringUtils.isEmpty(arrs[2])) {

                    ybond.setBillingDate(DateUtils.CSTToDate(arrs[2]));
                }

                ybond.setPrice(arrs[3]);

                ybond.setRecipient(arrs[4]);

                if (!StringUtils.isEmpty(arrs[5])) {

                    ybond.setRecipientDate(DateUtils.CSTToDate(arrs[5]));
                }


                Ybond z = this.ybondMapper.selectByUniqueCode(arrs[0]);

                if (z != null) {

                    ybond.setId(z.getId());

                    this.ybondMapper.updateByPrimaryKeySelective(ybond);
                } else {

                    this.ybondMapper.insertSelective(ybond);
                }

                succNum++;

            } catch (Exception e) {

                failList.add("唯一编码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));

        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }


    public Map<String, Object> importZsaleExcel(List<String[]> list) {

        List<String> failList = new ArrayList<>();

        int succNum = 0;

        for (String[] arrs : list) {

            Zsale zsale = new Zsale();
            try {

                if (arrs[0].equals("")) {
                    break;
                }

                zsale.setSn(arrs[0]);

                zsale.setSchoolName(arrs[1]);

                zsale.setConsignee(arrs[2]);

                zsale.setConsigneePhone(arrs[3]);

                zsale.setConsigneeAdress(arrs[4]);

                zsale.setDepartment(arrs[5]);

                zsale.setSalesManager(arrs[6]);

                zsale.setSalesPhone(arrs[7]);

                if (!arrs[8].equals("")) {

                    zsale.setQueryDate(DateUtils.CSTToDate(arrs[8]));

                    zsale.setTrialStartDate(DateUtils.CSTToDate(arrs[8]));
                }

                if (!arrs[9].equals("")) {

                    zsale.setTrialEndDate(DateUtils.CSTToDate(arrs[9]));
                }

                Zagents zg = this.zagentsMapper.selectBySn(arrs[0]);

                if (zg != null) {
                    continue;
                }


                Zsale z = this.zsaleMapper.selectBySn(arrs[0]);

                if (z != null) {

                    zsale.setId(z.getId());

                    this.zsaleMapper.updateByPrimaryKeySelective(zsale);
                } else {

                    Robot robot = this.robotMapper.getRobotSn(arrs[0]);

                    if (robot == null) {

                        failList.add("机器人SN码不存在:" + arrs[0]);
                        continue;
                    }

                    zsale.setRobotId(robot.getRobotId());

                    Zrobot zrobot = this.zrobotMapper.selectByRobotId(robot.getRobotId());

                    if (zrobot == null) {

                        Zrobot record = new Zrobot();

                        record.setRobotId(robot.getRobotId());

                        this.zrobotMapper.insertSelective(record);
                    }

                    SchoolRobot sr = this.schoolRobotMapper.selectRobotById(robot.getRobotId());

                    if (sr != null) {

                        zsale.setSchoolId(sr.getSchoolId());
                    }

                    this.zsaleMapper.insertSelective(zsale);
                }

                succNum++;

            } catch (Exception e) {

                failList.add("机器人SN码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));


        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }


    public Map<String, Object> importYorderDetailsExcel(List<String[]> list) {

        if (list == null || list.size() == 0) {
            return CommonUtil.resultMsg("FAIL", "没有数据");
        }

        log.info("list:" + list.size());
        List<String> failList = new ArrayList<>();

        int succNum = 0;
        int count = 0;

        for (String[] arrs : list) {
            try {

                if (count == 0) {
                    String[] titleArr = new String[]{"唯一编码", "出货日期", "部门", "业务员", "订单编号", "客户属性", "订单类型"};
                    boolean isCanOpe = true;
                    for (int i = 0; i < titleArr.length; i++) {
                        if (!arrs[i].equals(titleArr[i])) {
                            isCanOpe = false;
                            break;
                        }
                    }
                    if (isCanOpe) {
                        //数据正确
                        count = 1;
                        continue;
                    } else {
                        log.error("表格格式不正确");
                        Map<String, Object> result = new HashMap<>();
                        result.put("resultCode", "FAIL");
                        result.put("msg", "失败");
                        result.put("failList", failList);
                        result.put("failNum", Integer.valueOf(failList.size()));
                        return result;
                    }
                }
                count = count + 1;


                if (arrs[0] == null || "".equals(arrs[0]) || "#REF!".equals(arrs[0]) ||
                        arrs[9] == null || "".equals(arrs[9]) || "#REF!".equals(arrs[9])
                ) {
                    continue;
                }
                log.info(count + "," + JSONObject.toJSONString(arrs));
                YorderDetails yorderDetails = new YorderDetails();
                yorderDetails.setUniqueCode(arrs[0]);

                if (!arrs[1].equals("") && !"#REF!".equals(arrs[1])) {
                    yorderDetails.setShippingDate(DateUtils.CSTToDate(arrs[1]));
                }

                yorderDetails.setDepartment(arrs[2]);

                yorderDetails.setSalesman(arrs[3]);

                yorderDetails.setOrderNo(arrs[4]);

                yorderDetails.setClientProperty(arrs[5]);

                yorderDetails.setOrderType(arrs[6]);

                if (!StringUtils.isEmpty(yorderDetails.getOrderType()) &&
                        "服务费,退货,续租,退差价".contains(yorderDetails.getOrderType())) {

                    yorderDetails.setIfSynchro("1");
                }


                yorderDetails.setShippingCompanyCode(arrs[7]);

                yorderDetails.setCompany(arrs[8]);

                yorderDetails.setProductCode(arrs[9]);


                yorderDetails.setProductName(arrs[10]);


                yorderDetails.setRobotModel(arrs[11]);

                yorderDetails.setPackagingUnit(arrs[12]);


                if (!arrs[13].equals("")) {

                    yorderDetails.setNum(Integer.valueOf((new BigDecimal(arrs[13])).intValue()));
                }

                if (!arrs[14].equals("")) {

                    yorderDetails.setTaxUnitPrice(new BigDecimal(arrs[14]));
                }

                if (!arrs[15].equals("")) {

                    yorderDetails.setTaxPrice(new BigDecimal(arrs[15]));
                }

                yorderDetails.setPaymentStatus(arrs[16]);

                if (!arrs[17].equals("")) {

                    yorderDetails.setPayPrice(new BigDecimal(arrs[17]));
                }

                yorderDetails.setConsignee1(arrs[18]);

                yorderDetails.setConsignee2(arrs[19]);

                yorderDetails.setConsigneePhone1(arrs[20].replace(".00", ""));

                yorderDetails.setConsigneePhone2(arrs[21].replace(".00", ""));

                yorderDetails.setConsigneeAddress(arrs[22]);

                yorderDetails.setOrderDemand(arrs[23]);

                yorderDetails.setRemark(arrs[24]);

                yorderDetails.setCard(arrs[25]);

                yorderDetails.setCollectionOrNot(arrs[26]);

                yorderDetails.setCollectionAmount(arrs[27]);

                yorderDetails.setInvoiceOrNot(arrs[28]);

                yorderDetails.setInvoiceType(arrs[29]);

                yorderDetails.setInvoiceHeader(arrs[30]);

                yorderDetails.setPriceSystem(arrs[31]);

                yorderDetails.setShipmentNoticeNo(arrs[32]);

                yorderDetails.setEnterToT(arrs[33]);

                yorderDetails.setOrdertNo(arrs[34]);

                yorderDetails.setShipmentReturn(arrs[35]);

                if (!StringUtils.isEmpty(yorderDetails.getShipmentReturn()) &&
                        "服务费,退货,续租,退差价".contains(yorderDetails.getShipmentReturn())) {

                    yorderDetails.setIfSynchro("1");
                }


                yorderDetails.setFinanceClassification(arrs[36]);

                yorderDetails.setReportClassification(arrs[37]);

                yorderDetails.setShipmentInArrears(arrs[38]);

                yorderDetails.setShipTime(arrs[39]);

                yorderDetails.setSalesManager(arrs[40]);

                yorderDetails.setOrderTypeClassification(arrs[41]);

                yorderDetails.setCommissionReckon(arrs[42]);

                yorderDetails.setRobotOrNot(arrs[43]);

                yorderDetails.setRemark2(arrs[44]);

                if (!StringUtils.isEmpty(arrs[48].replace(".00", ""))) {
                    yorderDetails.setAgentModelId(Integer.parseInt(arrs[48].replace(".00", "")));
                } else {
                    yorderDetails.setAgentModelId(null);
                }
                if (!StringUtils.isEmpty(arrs[49].replace(".00", ""))) {
                    yorderDetails.setAgentId(Long.parseLong(arrs[49].replace(".00", "")));
                } else {
                    yorderDetails.setAgentId(null);
                }
                if (!StringUtils.isEmpty(arrs[50].replace(".00", ""))) {
                    //不为空的时候修改一下，都在不变
                    yorderDetails.setSystemTag(Integer.parseInt(arrs[50].replace(".00", "")));
                }

                if (!StringUtils.isEmpty(yorderDetails.getProductName())) {
                    Ycost ycost = this.ycostMapper.selectByProduct(yorderDetails.getProductName());
                    if (ycost != null) {
                        yorderDetails.setProductCost(ycost.getPrice().multiply(new BigDecimal(yorderDetails.getNum().intValue())));
                    }
                }
                YorderDetails yd = this.yorderDetailsMapper.selectByUniqueCode(arrs[0]);

                if ("YF000101".equals(yorderDetails.getProductCode()) || "欠款".equals(yorderDetails.getPaymentStatus())) {
                    yorderDetails.setCommissionRate(BigDecimal.ZERO);
                    yorderDetails.setCommissionAmount(BigDecimal.ZERO);
                } else {
                    // 查询分成比率
                    Map<String, CommissionRatioConfig> normalEmployeeConfigMap = queryNormalEmployeeCommissionConfig(CommissionType.ORDER);
                    CommissionRatioConfig commissionConfig = normalEmployeeConfigMap.get(yorderDetails.getDepartment());
                    if (null != commissionConfig && !StringUtils.isEmpty(yorderDetails.getRobotOrNot())) {
                        // 机器人比率
                        BigDecimal ratio = "是".equals(yorderDetails.getRobotOrNot()) ? commissionConfig.getRobotRatio() : commissionConfig.getPartRatio();
                        yorderDetails.setCommissionRate(ratio);
                        yorderDetails.setCommissionAmount(ratio.multiply(yorderDetails.getTaxPrice()));
                    }
                }

                if (yd == null) {
                    yorderDetails.setUniqueOrderNo(arrs[34] + "_import_" + arrs[0]);
                    this.yorderDetailsMapper.insertSelective(yorderDetails);
                } else {
                    yorderDetails.setId(yd.getId());
                    this.yorderDetailsMapper.updateByPrimaryKeySelective(yorderDetails);
                }
                succNum++;

            } catch (Exception e) {
                e.printStackTrace();
                failList.add("唯一编码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));

        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }


    public Map<String, Object> importYpaidDetailsExcel(List<String[]> list, String userName) {
        if (list == null || list.size() == 0) {
            return CommonUtil.resultMsg("FAIL", "没有数据");
        }
        List<String> failList = new ArrayList<>();
        int succNum = 0;
        int count = 0;
        for (String[] arrs : list) {

            if (count == 0) {
                if (arrs[0].equals("唯一编码")
                        && arrs[1].equals("日期")
                        && arrs[2].equals("客户名称")
                        && arrs[3].equals("收款金额")
                        && arrs[4].equals("新签保证金")
                        && arrs[10].equals("电话招商销售")
                        && arrs[11].equals("招商分成比例")
                        && arrs[12].equals("电话招商分成比例")
                ) {
                    count = 1;
                    //数据正确
                    continue;
                } else {
                    log.error("表格格式不正确");
                    Map<String, Object> result = new HashMap<>();
                    result.put("resultCode", "FAIL");
                    result.put("msg", "失败");
                    result.put("failNum", Integer.valueOf(failList.size() - 1));
                    return result;
                }

            }
            log.info(JSONObject.toJSONString(arrs));
            YpaidDetails ypaidDetails = new YpaidDetails();
            try {
                if (arrs[0].equals("")) {
                    break;
                }
                if (arrs[0].equals("唯一编码")) {
                    break;
                }
                // 如果销售分成比例和电话分成比例都为空，则break
                if (StringUtils.isEmpty(arrs[11]) && StringUtils.isEmpty(arrs[12])) {
                    log.info("销售分成比例和电话分成比例都为空,for 唯一编号=" + arrs[0]);
//                    break;
                }
                ypaidDetails.setUniqueCode(arrs[0]);

                if (!arrs[1].equals("")) {
                    ypaidDetails.setCollectionDate(DateUtils.CSTToDate(arrs[1]));
                }

                ypaidDetails.setCustomerName(arrs[2]);
                if (!arrs[3].equals("")) {
                    ypaidDetails.setAmount(new BigDecimal(arrs[3]));
                }

                ypaidDetails.setBond(arrs[4]);

                ypaidDetails.setRemark(arrs[5]);

                ypaidDetails.setDepartment(arrs[6]);

                ypaidDetails.setSalesManager(arrs[7]);
                /**
                 * 是否是机器人账户：是或者否
                 */
                ypaidDetails.setExp3(arrs[8]);
                /**
                 * 用户类型：幼教或者小学
                 */
                ypaidDetails.setExp2(arrs[9]);

                // 处理电话销售
                ypaidDetails.setPhoneSales(StringUtils.isEmpty(arrs[10]) ? null : arrs[10].trim());
                // 处理销售分成比例
                if (!StringUtils.isEmpty(arrs[11])) {
                    // 处理百分比字符串，如 "3%" 转换为 0.03
                    String salesRatio = arrs[11].replace("%", "").trim();
                    BigDecimal salesCommissionRatio = new BigDecimal(salesRatio);
                    ypaidDetails.setSalesCommissionRatio(salesCommissionRatio);
                } else {
                    // 如果销售分成比例为空，设置为 0
                    ypaidDetails.setSalesCommissionRatio(BigDecimal.ZERO);
                }
                // 计算销售分成金额 = 收款金额 * 销售分成比例
                if (ypaidDetails.getAmount() != null) {
                    BigDecimal salesCommissionAmount = ypaidDetails.getAmount().multiply(ypaidDetails.getSalesCommissionRatio()).setScale(2, RoundingMode.HALF_UP);
                    ypaidDetails.setSalesCommissionAmount(salesCommissionAmount);
                }

                // 处理电话分成比例
                if (!StringUtils.isEmpty(arrs[12])) {
                    // 处理百分比字符串，如 "3%" 转换为 0.03
                    String phoneRatio = arrs[12].replace("%", "").trim();
                    BigDecimal phoneCommissionRatio = new BigDecimal(phoneRatio);
                    ypaidDetails.setPhoneCommissionRatio(phoneCommissionRatio);
                } else  {
                    // 如果销售分成比例为空，设置为 0
                    ypaidDetails.setPhoneCommissionRatio(BigDecimal.ZERO);
                }

                if(ypaidDetails.getPhoneCommissionRatio().add(ypaidDetails.getSalesCommissionRatio()).compareTo(BigDecimal.valueOf(0.03))>0){
                    failList.add("唯一编码:" + arrs[0]+"，分成比例错误");
                    continue;
                }
                // 计算电话分成金额 = 收款金额 * 电话分成比例
                if (ypaidDetails.getAmount() != null) {
                    BigDecimal phoneCommissionAmount = ypaidDetails.getAmount().multiply(ypaidDetails.getPhoneCommissionRatio()).setScale(2, RoundingMode.HALF_UP);
                    ypaidDetails.setPhoneCommissionAmount(phoneCommissionAmount);
                }

                // 计算总分成金额 = 销售分成金额 + 电话分成金额
                BigDecimal salesAmount = ypaidDetails.getSalesCommissionAmount() != null ? ypaidDetails.getSalesCommissionAmount() : BigDecimal.ZERO;
                BigDecimal phoneAmount = ypaidDetails.getPhoneCommissionAmount() != null ? ypaidDetails.getPhoneCommissionAmount() : BigDecimal.ZERO;
                ypaidDetails.setTotalCommissionAmount(salesAmount.add(phoneAmount).setScale(2, RoundingMode.HALF_UP));

                YpaidDetails yd = this.ypaidDetailsMapper.selectByUniqueCode(arrs[0]);
                if (yd == null) {
                    this.ypaidDetailsMapper.insertSelective(ypaidDetails);
                    AuditLog log = new AuditLog();
                    log.setOperationType(DbOperation.INSERT.getName());
                    log.setNewData(JSONObject.toJSONString(ypaidDetails));
                    log.setTableName("y_paid_details");
                    log.setUser(userName);
                    logMapper.insertAuditLog(log);
                    succNum++;
                } else {
                    if (yd.getDataSystem() != null && yd.getDataSystem().equals("1")) {
                        failList.add("唯一编码:" + arrs[0] + ";老数据，不能修改");
                    } else {
                        ypaidDetails.setId(yd.getId());
                        log.info("need update" + yd.getId());
                        if (!ypaidDetails.toString().equals(yd.toString())) {
                            log.info("need update");
                            AuditLog log = new AuditLog();
                            log.setOperationType(DbOperation.UPDATE.getName());
                            log.setOldData(JSONObject.toJSONString(yd));
                            log.setNewData(JSONObject.toJSONString(ypaidDetails));
                            log.setRecordId(yd.getId());
                            log.setTableName("y_paid_details");
                            log.setUser(userName);
                            logMapper.insertAuditLog(log);
                            this.ypaidDetailsMapper.updateByPrimaryKeySelective(ypaidDetails);
                        }
                        succNum++;
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                failList.add("唯一编码:" + arrs[0]);
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("resultCode", "SUCCESS");
        result.put("msg", "成功");
        result.put("succNum", Integer.valueOf(succNum));
        result.put("failList", failList);
        result.put("failNum", Integer.valueOf(failList.size()));
        return result;
    }


    public Map<String, Object> importYcollectionExcel(List<String[]> list) {

        List<String> failList = new ArrayList<>();

        int succNum = 0;

        for (String[] arrs : list) {

            log.info(JSONObject.toJSONString(arrs));

            Ycollection ycollection = new Ycollection();

            try {
                if (arrs[0].equals("")) {
                    break;
                }
                if (arrs[0].equals("唯一编码")) {
                    break;
                }


                ycollection.setUniqueCode(arrs[0]);

                if (!arrs[1].equals("")) {

                    ycollection.setOrderDate(DateUtils.CSTToDate(arrs[1]));
                }

                ycollection.setDepartment(arrs[2]);

                ycollection.setCustomerName(arrs[3]);

                if (!arrs[4].equals("")) {

                    ycollection.setNum(Integer.valueOf((new BigDecimal(arrs[4])).intValue()));
                }

                ycollection.setProductName(arrs[5]);

                if (!arrs[6].equals("")) {

                    ycollection.setPrice(new BigDecimal(arrs[6]));
                }

                ycollection.setAccountingPeriod(arrs[7]);

                if (!arrs[8].equals("")) {

                    ycollection.setAnswerCollectDate(DateUtils.CSTToDate(arrs[8]));
                }

                if (!arrs[9].equals("")) {

                    ycollection.setArrears(new BigDecimal(arrs[9]));
                }

                ycollection.setSalesman(arrs[10]);

                ycollection.setRemark(arrs[11]);

                if (!arrs[12].equals("")) {

                    ycollection.setCollectDate(DateUtils.CSTToDate(arrs[12]));
                }


                ycollection.setCollectionPrice(arrs[13]);

                if (!arrs[14].equals("")) {

                    ycollection.setAnswerPrice(new BigDecimal(arrs[14]));
                }


                Ycollection yd = this.ycollectionMapper.selectByUniqueCode(arrs[0]);

                if (yd == null) {

                    this.ycollectionMapper.insertSelective(ycollection);
                } else {

                    ycollection.setId(yd.getId());

                    this.ycollectionMapper.updateByPrimaryKeySelective(ycollection);
                }

                succNum++;

            } catch (Exception e) {
                e.printStackTrace();
                failList.add("唯一编码:" + arrs[0]);
            }
        }


        Map<String, Object> result = new HashMap<>();

        result.put("resultCode", "SUCCESS");

        result.put("msg", "成功");

        result.put("succNum", Integer.valueOf(succNum));

        result.put("failList", failList);

        result.put("failNum", Integer.valueOf(failList.size()));

        return result;
    }


    public Map<String, Object> unbound(JSONObject jo) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        String robotId = jo.getString("robotId");

        Map<String, Object> verifyResult = CommonUtil.verifyNull(new String[]{robotId});

        if (verifyResult != null) {

            return CommonUtil.resultMsg("FAIL", "参数为空");
        }

        int status = 1;

        SchoolRobot sr = new SchoolRobot();

        sr.setRobotId(robotId);

        sr.setSchoolId(Long.valueOf(-1L));

        sr.setStatus(Integer.valueOf(0));

        this.schoolRobotMapper.updateByRobotId(sr);


        Zsale zsale = this.zsaleMapper.selectByRobotId(robotId);

        status = zsale.getStatus().intValue();

        if (zsale != null) {

            zsale.setStatus(Integer.valueOf(0));

            this.zsaleMapper.updateByPrimaryKeySelective(zsale);
        }


        Robot r = new Robot();

        r.setRobotId(robotId);

        r.setChannel("CH10000");

        this.robotMapper.edit(r);


        Zrobot record = new Zrobot();

        record.setLockStatus(Integer.valueOf(1));

        record.setBanClassStatus(Integer.valueOf(1));

        record.setRobotId(robotId);

        this.zrobotMapper.updateByPrimaryKeySelective(record);


        XfaboSms xs = this.xfaboSmsMapper.getMemberByPhone(zsale.getSalesPhone());

        if (xs != null && status == 1) {

            XfaboSms zsmn = new XfaboSms();

            zsmn.setMemberId(xs.getId());

            zsmn.setSaleId(zsale.getId());

            zsmn.setNickName("尊敬的销售经理");

            zsmn.setTime(new Date());

            zsmn.setContent("您签定的" + zsale.getSchoolName() + "绑定的SN号" + zsale.getSn() + "机器人已经在" + DateUtils.format2(new Date()) + "解绑!!!");

            this.xfaboSmsMapper.insertSelective(zsmn);
        }

        return map;
    }


    public List<Yproduct> selectByProductCode(String xx) {

        List<Yproduct> list = this.yproductMapper.selectAll(xx);

        return list;
    }


    public Map<String, Object> getMarketingProfit(String month) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        List<YdepartProfit> list = this.ydepartProfitMapper.selectByMonth(month);

        if (list.size() == 0) {

            map.put("1", "0");

            map.put("2", "0");

            map.put("3", "0");

            map.put("4", "0");

            map.put("5", "0");

            map.put("6", "0");

            map.put("7", "0");

            map.put("8", "0");

            map.put("9", "0");

            map.put("10", "0");

            map.put("11", "0");

            map.put("12", "0");

            map.put("13", "0");

            map.put("14", "0");

            map.put("15", "0");

            map.put("16", "0");

            map.put("17", "0");

            return map;
        }

        int totalSales = 0;

        int totalCost = 0;

        int totalNum = 0;

        int totalBaseSalary = 0;

        int totalNetProfit = 0;

        int advertFee = 0;

        int grossProfit = 0;

        int commissionAmount = 0;

        int monthIncome = 0;

        BigDecimal operateFee = new BigDecimal(0);

        for (YdepartProfit ydp : list) {

            totalSales += ydp.getSalesVolume().intValue();

            totalCost += ydp.getTotalCost().intValue();

            totalNum += ydp.getPeopleNum().intValue();

            totalBaseSalary += ydp.getTotalBaseSalary().intValue();

            totalNetProfit += ydp.getNetProfit().intValue();

            advertFee += ydp.getAdvertFee().intValue();

            grossProfit += ydp.getGrossProfit().intValue();

            commissionAmount += ydp.getCommissionAmount().intValue();

            monthIncome += ydp.getMonthIncome().intValue();

            operateFee = ydp.getOperateFee();
        }

        monthIncome = totalBaseSalary + commissionAmount;

        int departmentProportion = (new BigDecimal(monthIncome)).multiply(operateFee).setScale(0, 4).intValue();

        BigDecimal grossMargin = (new BigDecimal(grossProfit)).divide(new BigDecimal(totalSales), 3, 4).multiply(new BigDecimal(100)).setScale(1, 4);

        BigDecimal totalNetProfitMargin = (new BigDecimal(totalNetProfit)).divide(new BigDecimal(totalSales), 3, 4).multiply(new BigDecimal(100)).setScale(1, 4);

        map.put("1", Integer.valueOf(totalSales));

        map.put("2", Integer.valueOf(totalCost));

        map.put("3", Integer.valueOf(totalNum));

        map.put("4", (new BigDecimal(totalSales)).divide(new BigDecimal(advertFee), 1, 4));

        map.put("5", grossMargin + "%");

        map.put("6", Integer.valueOf(totalBaseSalary));

        map.put("7", (new BigDecimal(monthIncome)).divide(new BigDecimal(totalNum), 1, 4));

        map.put("8", operateFee);

        map.put("9", Integer.valueOf(totalNetProfit - departmentProportion));

        map.put("10", Integer.valueOf(totalNetProfit));

        map.put("11", totalNetProfitMargin + "%");

        map.put("12", (new BigDecimal(totalSales)).divide(new BigDecimal(totalNum), 1, 4));

        map.put("13", Integer.valueOf(advertFee));

        map.put("14", Integer.valueOf(grossProfit));

        map.put("15", Integer.valueOf(commissionAmount));

        map.put("16", Integer.valueOf(monthIncome));

        map.put("17", Integer.valueOf(departmentProportion));

        return map;
    }

    public YdepartProfit selectByDepartAndMonth(String department, String month) {

        return this.ydepartProfitMapper.selectByDepartAndMonth(department, month);
    }

    public Map<String, Object> coursewareInfoUpload(@RequestBody JSONObject jo) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());


        return map;
    }

    public Map<String, Object> getCoursewareStatus(@RequestBody JSONObject jo) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        String robotId = jo.getString("robotId");

        map.put("data", this.coursewareInfoUploadMapper.selectByRobotId(robotId));

        return map;
    }

    public Map<String, Object> reportContractPdf(@RequestBody JSONObject jo) {

        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        JSONArray jSONArray = jo.getJSONArray("pdf");

        YcontractPdf record = new YcontractPdf();

        for (int a = 0; a < jSONArray.size(); a++) {

            record.setFilePdf(jSONArray.get(a).toString().replace(".pdf", ""));

            this.ycontractPdfMapper.insert(record);
        }

        return map;
    }

    /**
     * 模糊查询客户名称
     *
     * @param jo
     * @return
     */
    public Map<String, Object> getSimilarCustomer(@RequestBody JSONObject jo) {
        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
        String exp1 = jo.getString("customerName");
        log.info("expq:" + exp1);
        // 提取 companyName 列表
        List<String> companyNameList = new ArrayList<>();
        if (!StringUtils.isEmpty(exp1)) {
            YpaidDetails temp = new YpaidDetails();
            temp.setExp1(exp1);
            List<YpaidDetails> list = ypaidDetailsMapper.selectByParms(temp);
            for (YpaidDetails details : list) {
                if (!companyNameList.contains(details.getCustomerName())) {
                    companyNameList.add(details.getCustomerName());
                }
            }
        }

        HashMap<String, Object> obj = new HashMap<>();
        obj.put("nameList", companyNameList);
        map.put("data", obj);
        return map;
    }

    /**
     * 获取用户实收的信息
     */
    public Map<String, Object> getCustomerBond(@RequestBody JSONObject jo) {
        try {
            Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                    ResultMessage.SUCCESS.getValue());
            String customerName = jo.getString("customerName");
            String startDate = jo.getString("startDate");
            String endDate = jo.getString("endDate");
            log.info(jo.toJSONString());
            Map<String, Object> verifyResult = CommonUtil.verifyNull(customerName, startDate, endDate);
            if (verifyResult != null) {

                return CommonUtil.resultMsg("FAIL", "参数为空");
            }
            // 提取 companyName 列表
            YpaidDetails temp = new YpaidDetails();
            temp.setExp1(customerName);
            temp.setCollectionDateStart(startDate);
            temp.setCollectionDateEnd(endDate);
            List<YpaidDetails> list = ypaidDetailsMapper.selectByParms(temp);

            log.info("list info = " + list.size());
            // 初始化变量
            BigDecimal bondAllIncome = BigDecimal.ZERO; // 保证金收入
            BigDecimal bondReturned = BigDecimal.ZERO; // 保证金返还
            BigDecimal bondRemaining = BigDecimal.ZERO; // 保证金剩余

            BigDecimal amountAllIncome = BigDecimal.ZERO; // 实收收入
            BigDecimal amountReturned = BigDecimal.ZERO; // 实收返还
            BigDecimal amountRemaining = BigDecimal.ZERO; // 实收剩余

            HashMap<String, Integer> agentType = new HashMap<>();
            agentType.put("小学", 2);
            agentType.put("幼教", 1);
            for (YpaidDetails ypaidDetails : list) {

//                if (ypaidDetails.getExp2() != null) {
//                    if ("小学".equals(ypaidDetails.getExp2()) || "2".equals(ypaidDetails.getExp2())) {
//  agentType.put("小学", 2);
//                    } else if ("幼教".equals(ypaidDetails.getExp2()) || "1".equals(ypaidDetails.getExp2())) {
//  agentType.put("幼教", 1);
//                    }
//
//                }

                ypaidDetails.setCollectionDateStr(DateUtils.format(ypaidDetails.getCollectionDate()));
                // 计算保证金
                try {
                    BigDecimal bond = new BigDecimal(ypaidDetails.getBond());
                    if (bond.compareTo(BigDecimal.ZERO) >= 0) {
                        bondAllIncome = bondAllIncome.add(bond);
                    } else {
                        bondReturned = bondReturned.add(bond);
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
                // 计算实收金额
                try {
                    BigDecimal amount = ypaidDetails.getAmount();

                    if (amount.compareTo(BigDecimal.ZERO) >= 0) {
                        amountAllIncome = amountAllIncome.add(amount);
                    } else {
                        amountReturned = amountReturned.add(amount);
                    }
                } catch (Exception e) {
                    // 忽略异常
                    e.printStackTrace();
                }
            }




            // 计算盈余
            bondRemaining = bondAllIncome.add(bondReturned);
            amountRemaining = amountAllIncome.add(amountReturned);
            // 构建返回的字典
            Map<String, Object> dictReturn = new HashMap<>();
            dictReturn.put("allIncome", bondAllIncome.setScale(2, RoundingMode.HALF_UP));
            dictReturn.put("returned", bondReturned.setScale(2, RoundingMode.HALF_UP));
            dictReturn.put("remaining", bondRemaining.setScale(2, RoundingMode.HALF_UP));

            dictReturn.put("allAmountIncome", amountAllIncome.setScale(2, RoundingMode.HALF_UP));
            dictReturn.put("amountReturned", amountReturned.setScale(2, RoundingMode.HALF_UP));
            dictReturn.put("amountRemaining", amountRemaining.setScale(2, RoundingMode.HALF_UP));
            dictReturn.put("incomeList", list);

            ArrayList<Map<String, Object>> typeList = new ArrayList<>();

            log.info(JSONObject.toJSONString(typeList));


            if (!ObjectUtils.isEmpty(agentType)) {
                for (Map.Entry<String, Integer> integerStringEntry : agentType.entrySet()) {
                    Map<String, Object> tempMap = calculateIncome(customerName, integerStringEntry.getValue());
                    if (tempMap != null && tempMap.size() > 0) {
                        tempMap.put("name", integerStringEntry.getKey());
                        typeList.add(tempMap);
                    }
                }
            }
            dictReturn.put("newAmount", typeList);
            map.put("data", dictReturn);
            return map;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private Map<String, Object> calculateIncome(String currentName, Integer agentType) {
        // 创建返回的 余额
        Map<String, Object> dict_return=null;
        //agentType 1、幼教；2、小学
        //剩余金额
        List<AgentBalance> ZAgentBalanceList = zAgentBalanceMapper.selectByCustomerNameAndAgentModelId(currentName, agentType, 1);

        log.info(JSONObject.toJSONString(ZAgentBalanceList));
        BigDecimal robotBalanceAmount;
        BigDecimal otherBalanceAmount;

        if (!ObjectUtils.isEmpty(ZAgentBalanceList)) {
            dict_return = new HashMap<>();
            robotBalanceAmount = ZAgentBalanceList.get(0).getRobotAccountAmount();
            otherBalanceAmount = ZAgentBalanceList.get(0).getOtherAccountAmount();
            List<YpaidDetails> yList = null;
            try {
                YpaidDetails mYpaidDetails = new YpaidDetails();
                mYpaidDetails.setCustomerName(currentName);

                mYpaidDetails.setExp2(String.valueOf(agentType));
                yList = ypaidDetailsMapper.selectByCustomName(mYpaidDetails);
                log.info(agentType+","+JSONObject.toJSONString(mYpaidDetails)+""+yList.size());
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 初始化变量
            BigDecimal robot_all_income = robotBalanceAmount; // 机器人账户收入
            BigDecimal other_all_income = otherBalanceAmount;// 耗材账户收入

            BigDecimal robotAllReturn = BigDecimal.ZERO; // 机器人返还
            BigDecimal otherAllReturn = BigDecimal.ZERO; // 机器人返还
            if (!ObjectUtils.isEmpty(yList)) {

                // 遍历 yList
                for (YpaidDetails item : yList) {
                    log.info(JSONObject.toJSONString(item));
                    if (item != null) {
                        BigDecimal amount = item.getAmount();
                        if (amount != null) {
                            if (amount.compareTo(BigDecimal.ZERO) >= 0) {
                                // 正数收入累加
                                if ("是".equals(item.getExp3())) {
                                    robot_all_income = robot_all_income.add(amount);
                                } else if ("否".equals(item.getExp3())) {
                                    other_all_income = other_all_income.add(amount);
                                }
                            } else {
                                // 正数收入累加
                                if ("是".equals(item.getExp3())) {
                                    robotAllReturn = robotAllReturn.add(amount);
                                } else if ("否".equals(item.getExp3())) {
                                    otherAllReturn = otherAllReturn.add(amount);
                                }
                            }


                        }
                    }
                }
            }

            dict_return.put("allRobotAmountIncome", robot_all_income.setScale(2, RoundingMode.HALF_UP));
            dict_return.put("allOtherAmountIncome", other_all_income.setScale(2, RoundingMode.HALF_UP));
            dict_return.put("robotAllReturn", robotAllReturn.setScale(2, RoundingMode.HALF_UP));
            dict_return.put("otherAllReturn", otherAllReturn.setScale(2, RoundingMode.HALF_UP));


            dict_return.put("robotRemaining", (robot_all_income.subtract(robotAllReturn)).setScale(2, RoundingMode.HALF_UP));
            dict_return.put("otherRemaining", (other_all_income.subtract(otherAllReturn)).setScale(2, RoundingMode.HALF_UP));

            dict_return.put("total", (robot_all_income.add(other_all_income)).setScale(2, RoundingMode.HALF_UP));
            dict_return.put("totalReturn", (robotAllReturn.add(otherAllReturn)).setScale(2, RoundingMode.HALF_UP));
            dict_return.put("totalRemaining", (robot_all_income.add(other_all_income).subtract(robotAllReturn).subtract(otherAllReturn)).setScale(2, RoundingMode.HALF_UP));

            log.debug(dict_return);
            //查询订单金额
        }
        return dict_return;

    }


//    public void cal() {
//        BigDecimal robotAllCome = BigDecimal.ZERO; // 机器人账户收入
//        BigDecimal robotAllReturn = BigDecimal.ZERO; // 机器人返还
//        BigDecimal otherAllCome = BigDecimal.ZERO; // 机器人账户收入
//        BigDecimal otherAllReturn = BigDecimal.ZERO; // 机器人返还
//
//
//        BigDecimal robotAllCome_primary = BigDecimal.ZERO; // 机器人账户收入
//        BigDecimal robotAllReturn_primary = BigDecimal.ZERO; // 机器人返还
//        BigDecimal otherAllCome_primary = BigDecimal.ZERO; // 机器人账户收入
//        BigDecimal otherAllReturn_primary = BigDecimal.ZERO; // 机器人返还
//
//
//        BigDecimal otherRemaining = BigDecimal.ZERO;
//        BigDecimal robotRemaining = BigDecimal.ZERO;
//        BigDecimal robotRemaining_primary = BigDecimal.ZERO;
//
//        HashMap<String, Object> newAmount = new HashMap<>();
//        //剩余金额
//        List<AgentBalance> ZAgentBalanceList = ypaidDetailsMapper.selectByCustomerName(customerName);
//        if (!ObjectUtils.isEmpty(ZAgentBalanceList)) {
//
//            for (AgentBalance agentBalance : ZAgentBalanceList) {
//
//                BigDecimal robotBalanceAmount = agentBalance.getRobotAccountAmount();
//                BigDecimal otherBalanceAmount = agentBalance.getOtherAccountAmount();
//
//                if (agentBalance.getAgentModelId() != null) {
//                    if (agentBalance.getAgentModelId() == 1) {
//
//                        robotAllCome = robotAllCome.add(robotBalanceAmount);
//                        robotAllReturn = robotAllReturn.multiply(BigDecimal.valueOf(-1));
//                        robotRemaining = robotAllCome.subtract(robotAllReturn);
//
//                        newAmount.put("robotAllCome", (robotAllCome).setScale(2, RoundingMode.HALF_UP));
//                        newAmount.put("robotAllReturn", robotAllReturn.setScale(2, RoundingMode.HALF_UP));
//                        newAmount.put("robotRemaining", robotRemaining.setScale(2, RoundingMode.HALF_UP));
//
//                        otherAllCome = otherAllCome.add(otherBalanceAmount);
//                        otherAllReturn = (otherAllReturn.multiply(BigDecimal.valueOf(-1)));
//                        otherRemaining = otherAllCome.subtract(otherAllReturn);
//                    } else {
//                        robotAllCome_primary = robotAllCome_primary.add(robotBalanceAmount);
//                        robotAllReturn_primary = robotAllReturn_primary.multiply(BigDecimal.valueOf(-1));
//                        robotRemaining_primary = robotAllCome.subtract(robotAllReturn);
//                        newAmount.put("robotAllComePrimary", (robotAllCome_primary).setScale(2, RoundingMode.HALF_UP));
//                        newAmount.put("robotAllReturnPrimary", robotAllReturn_primary.setScale(2, RoundingMode.HALF_UP));
//                        newAmount.put("robotRemainingPrimary", robotRemaining_primary.setScale(2, RoundingMode.HALF_UP));
//
//                        otherAllCome = otherAllCome.add(otherBalanceAmount);
//                        otherAllReturn = (otherAllReturn.multiply(BigDecimal.valueOf(-1)));
//                        otherRemaining = otherAllCome.subtract(otherAllReturn);
//                    }
//
//
//                }
//
//            }
//
//        }
//
//        newAmount.put("otherAllCome", otherAllCome.setScale(2, RoundingMode.HALF_UP));
//        newAmount.put("otherAllReturn", otherAllReturn.setScale(2, RoundingMode.HALF_UP));
//        newAmount.put("otherRemaining", otherRemaining.setScale(2, RoundingMode.HALF_UP));
//
//        newAmount.put("total", (robotAllCome.add(otherAllCome)).setScale(2, RoundingMode.HALF_UP));
//        newAmount.put("totalReturn", (robotAllReturn.add(otherAllReturn)).setScale(2, RoundingMode.HALF_UP));
//        newAmount.put("totalRemaining", (robotRemaining.add(otherRemaining)).setScale(2, RoundingMode.HALF_UP));
//
//        dictReturn.put("newAmount", newAmount);
//    }


    @Override
    public Map<String, Object> selectAllAgent() {
        // 用于存储查询结果的 Map
        Map<String, Object> dictInfo = null;
        try {
            dictInfo = new HashMap<>();
            List<Agent> agentList = agentMapper.getAgents();
            for (Agent agent : agentList) {
                Object value = StringUtils.isEmpty(agent.getIfDiscount()) ? 100 : agent.getIfDiscount();
                float result = Float.parseFloat(value.toString());
                dictInfo.put(agent.getCustomerName(), result);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return dictInfo;
    }

    @Override
    public Map<String, Object> getYpaidDetails(JSONObject jo) {

        return null;
    }

    @Override
    public List<YpaidDetails> selectPaidByDate(String startDate, String endDate, String customerName) {
        YpaidDetails param = new YpaidDetails();
        param.setCollectionDateStart(startDate);
        param.setCollectionDateEnd(endDate);
        param.setCustomerName(customerName);
        List<YpaidDetails> ypaidDetails = ypaidDetailsMapper.selectPaidByParms(param);
        return ypaidDetails;
    }

    @Override
    public List<Member> selectCommissionMembers(Member paramMember) {
        XDepartmentCommonly commonlyParams = new XDepartmentCommonly();
        commonlyParams.setExp1("TEAM");
        List<XDepartmentCommonly> xDepartmentCommonlies = departmentCommonlyMapper.selectByParms(commonlyParams);
        // 参与分成的部门名称
        List<String> commissionDepartmentList = xDepartmentCommonlies.stream().map(XDepartmentCommonly::getName).collect(Collectors.toList());
        // 查询人员部门信息
        List<Member> members = memberMapper.selectMembers(paramMember);
        return members.stream().filter(member -> commissionDepartmentList.contains(member.getDepartment())).filter(member -> {
            // 过滤掉非当月离职的
            if (Integer.valueOf(1).equals(member.getResignedStatus()) && member.getResignedDate() != null) {
                String currentMonth = DateUtils.format(new Date(), "yyyy-MM");
                String resignedMonth = DateUtils.format(member.getResignedDate(), "yyyy-MM");
                return currentMonth.equals(resignedMonth);
            }
            return true;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CommissionDetail> selectCommissionByDate(String startDate, String endDate) {
        List<CommissionDetail> result = new ArrayList<>();

        // 查询人员部门信息
        Member paramMember = new Member();
        List<Member> members = selectCommissionMembers(paramMember);
        Map<String, Member> memberMap = new HashMap<>();
        for (Member member : members) {
            memberMap.put(member.getName(), member);
        }

        // 需要排除总监 directorConfigMap
        Map<String, CommissionRatioConfig> directorConfigMap = queryDirectorMap(CommissionType.REAL);
        Map<String, CommissionRatioConfig> directorNameConfigMap = queryDirectorNameMap();

        // 查询实收表
        YpaidDetails ypaidDetailParams = new YpaidDetails();
        ypaidDetailParams.setCollectionDateStart(startDate);
        ypaidDetailParams.setCollectionDateEnd(endDate);

        // 按销售经理分组汇总
        List<YpaidDetails> salesPaidDetail2List = ypaidDetailsMapper.selectCommissionBySales(ypaidDetailParams);
        // 排除线下网络招商四部
        List<YpaidDetails> salesPaidDetailList = salesPaidDetail2List.stream().filter(e -> !"线下网络招商四部".equals(e.getDepartment())).collect(Collectors.toList());
        Map<String, CommissionDetail> salesCommissionMap = new HashMap<>();

        // 处理销售经理的数据
        processCommissionDetails(salesPaidDetailList, salesCommissionMap, memberMap, true, directorNameConfigMap);

        // 按电话销售分组汇总
        ypaidDetailParams.setExcludeDepartment("线下网络招商四部");
        List<YpaidDetails> phoneSalesPaidDetailList = ypaidDetailsMapper.selectCommissionByPhoneSales(ypaidDetailParams);

        // 处理电话销售的数据
        processCommissionDetails(phoneSalesPaidDetailList, salesCommissionMap, memberMap, false, directorNameConfigMap);
        // 招商经理 特殊处理线下网络招商四部退款
        processCommissionSpecialRefund(startDate, endDate, salesCommissionMap, memberMap);

        // 处理总监级别的提成数据
        processDirectorCommissionDetails(startDate, endDate, directorConfigMap, salesCommissionMap, memberMap);


        // 查询订单表
        // 订提成计算逻辑需获取配置
        // 运费YF000101
        // where shipping_date >=a and <b && payment_status!=欠款&&product_code!=YF000101 group by salesman,robot_or_not
        Map<String, CommissionRatioConfig> orderDirectorConfigMap = queryDirectorMap(CommissionType.ORDER);
        Map<String, CommissionRatioConfig> orderDirectorNameConfigMap = queryDirectorNameMap();

        YorderDetails yorderDetailsParams = new YorderDetails();
        yorderDetailsParams.setShippingDateStart(startDate);
        yorderDetailsParams.setShippingDateEnd(endDate);
        // 审核通过
        yorderDetailsParams.setExp1("1");
        yorderDetailsParams.setPaymentStatus("欠款");
        // 运费
        yorderDetailsParams.setProductCode("YF000101");
        List<YorderDetails> yorderDetailList = yorderDetailsMapper.selectCommissionByParms(yorderDetailsParams);

        // 处理订单数据并整合到result中
        processOrderCommissionDetails(yorderDetailList, salesCommissionMap, memberMap, orderDirectorNameConfigMap);
        // 处理招商经理运费
        processOrderFreightDetails(salesCommissionMap, memberMap, orderDirectorNameConfigMap, startDate, endDate);


        // 处理总监线上订单的提成
        processDirectorOrderCommissionDetails(startDate, endDate, orderDirectorConfigMap, salesCommissionMap, memberMap);

        // 特殊处理 线下网络运营部
        processCommissionSpecialOrderForPaid(startDate, endDate, salesCommissionMap, memberMap, orderDirectorConfigMap);

        // 运费增加到总order金额中
        addFrightToOrderAmount(salesCommissionMap);

        // 更新result列表
        result.addAll(salesCommissionMap.values());
        // 处理工资
        processSalary(result);
        return result;
    }

    private void addFrightToOrderAmount(Map<String, CommissionDetail> salesCommissionMap) {
        salesCommissionMap.values().stream()
                .filter(commission -> commission.getFreightAmount() != null)
                .forEach(commission -> {
                    BigDecimal currentOrderAmount = Optional.ofNullable(commission.getOrderAmount()).orElse(BigDecimal.ZERO);
                    commission.setOrderAmount(currentOrderAmount.add(commission.getFreightAmount()).setScale(2, RoundingMode.HALF_UP));
                });
    }

    private void processCommissionSpecialOrderForPaid(String startDate, String endDate, Map<String, CommissionDetail> salesCommissionMap, Map<String, Member> memberMap, Map<String, CommissionRatioConfig> orderDirectorConfigMap) {
        // 查询该部门下的所有实收数据 不计入提成
        String department = "线下网络运营部";
        YpaidDetails paidDetailsQuery = buildPaidQuery(startDate, endDate, department);
        paidDetailsQuery.setDepartment(null);
        List<String> nameList = getDepartmentMembers(department);
        if (nameList == null) {
            return;
        }
        paidDetailsQuery.setNameList(nameList);
        List<YpaidDetails> departmentPaidDetails = ypaidDetailsMapper.selectPaidByParms(paidDetailsQuery);
        CommissionRatioConfig config = orderDirectorConfigMap.get(department);
        String salesManager = config.getName();
        // 汇总数据到对应的CommissionDetail对象
        CommissionDetail commission = getOrCreateCommissionDetail(salesCommissionMap, salesManager, memberMap, null, department);
        departmentPaidDetails.stream()
                .filter(detail -> detail.getAmount() != null)
                .forEach(detail -> {
                    BigDecimal currentAmount = Optional.ofNullable(commission.getSalesAmount()).orElse(BigDecimal.ZERO);
                    commission.setSalesAmount(currentAmount.add(detail.getAmount()));
                });
    }

    private void processCommissionSpecialRefund(String startDate, String endDate, Map<String, CommissionDetail> salesCommissionMap, Map<String, Member> memberMap) {
        // 线下网络招商四部 销售经理退款比率SalesCommissionRatio, 电话销售经理退款比率PhoneCommissionRatio
        String specialDepartment = "线下网络招商四部";
        YpaidDetails specialPaidDetailsQuery = buildPaidQuery(startDate, endDate, specialDepartment);
        List<YpaidDetails> specialDepartmentPaidDetails = ypaidDetailsMapper.selectPaidByParms(specialPaidDetailsQuery);
        // PhoneSales=>{PhoneCommissionRatio=>beans}
        Map<String, Map<BigDecimal, List<YpaidDetails>>> groupedByPhoneSalesAndRatio = specialDepartmentPaidDetails.stream()
                .filter(e -> !StringUtils.isEmpty(e.getPhoneSales()))
                .collect(Collectors.groupingBy(
                        YpaidDetails::getPhoneSales,
                        Collectors.groupingBy(YpaidDetails::getPhoneCommissionRatio)
                ));
        for (Map.Entry<String, Map<BigDecimal, List<YpaidDetails>>> phoneSalesEntry : groupedByPhoneSalesAndRatio.entrySet()) {
            String phoneSales = phoneSalesEntry.getKey();

            // Skip if phoneSales is null or empty
            if (StringUtils.isEmpty(phoneSales)) {
                continue;
            }

            // Get or create commission detail for this phone sales person
            CommissionDetail commission = getOrCreateCommissionDetail(salesCommissionMap, phoneSales, memberMap, null, specialDepartment);

            // Process each commission ratio group for this phone sales person
            Map<BigDecimal, List<YpaidDetails>> ratioDetailsMap = phoneSalesEntry.getValue();
            for (Map.Entry<BigDecimal, List<YpaidDetails>> ratioEntry : ratioDetailsMap.entrySet()) {
                BigDecimal ratio = ratioEntry.getKey();
                List<YpaidDetails> details = ratioEntry.getValue();

                // Create CommissionRefund object and set values
                CommissionRefund refund = new CommissionRefund();
                refund.setCommissionRatio(ratio);

                // Calculate total sales amount for this ratio
                BigDecimal totalSalesAmount = details.stream()
                        .map(YpaidDetails::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                refund.setRefundAmount(totalSalesAmount);

                // Calculate commission amount based on ratio
                BigDecimal totalCommissionAmount = totalSalesAmount.multiply(ratio);
                BigDecimal currentCommission = Optional.ofNullable(commission.getCommissionAmount()).orElse(BigDecimal.ZERO);
                commission.setCommissionAmount(currentCommission.add(totalCommissionAmount));

                // Add refund to commission's refundList
                List<CommissionRefund> refundList = commission.getRefundList();
                if (refundList == null) {
                    refundList = new ArrayList<>();
                    commission.setRefundList(refundList);
                }
                refundList.add(refund);
            }
        }
    }

    private void processSalary(List<CommissionDetail> result) {
        if (result == null || result.isEmpty()) {
            return;
        }
        XDepartmentCommonly departmentCommonlyParams = new XDepartmentCommonly();
        departmentCommonlyParams.setExp1("L");
        List<XDepartmentCommonly> xDepartmentCommonlies = departmentCommonlyMapper.selectByParms(departmentCommonlyParams);
        if (xDepartmentCommonlies == null || xDepartmentCommonlies.isEmpty()) {
            return;
        }
        // level=>salary
        Map<String, XDepartmentCommonly> salaryMap = xDepartmentCommonlies.stream()
                .filter(dept -> dept.getName() != null && dept.getSalary() != null)
                .collect(Collectors.toMap(XDepartmentCommonly::getName, Function.identity(), (oldValue, newValue) -> newValue
                ));
        for (CommissionDetail detail : result) {
            if (detail.getMember() == null || detail.getMember().getLevel() == null) {
                continue;
            }

            String level = detail.getMember().getLevel();
            XDepartmentCommonly salaryConfig = salaryMap.get(level);
            if (salaryConfig == null || salaryConfig.getSalary() == null) {
                continue;
            }

            BigDecimal salary = salaryConfig.getSalary();
            // 设置基本工资
            detail.setBaseSalary(BigDecimal.valueOf(0.8D).multiply(salary));

            // 默认绩效工资为0
            detail.setPerformSalary(BigDecimal.ZERO);

            // 如果没有销售额或目标值，则跳过绩效计算
            if (salaryConfig.getExp2() == null || (detail.getSalesAmount() == null && detail.getOrderAmount() == null)) {
                continue;
            }

            // 计算绩效工资
            BigDecimal targetAmount = new BigDecimal(salaryConfig.getExp2()).multiply(new BigDecimal("10000"));
            BigDecimal salesAmount = Optional.ofNullable(detail.getSalesAmount()).orElse(BigDecimal.ZERO);
            BigDecimal orderAmount = Optional.ofNullable(detail.getOrderAmount()).orElse(BigDecimal.ZERO);
            BigDecimal allAmount = salesAmount.add(orderAmount);
            if (allAmount.compareTo(targetAmount) > 0) {
                // 销售额超过目标，绩效工资为基本工资的20%
                detail.setPerformSalary(salary.multiply(new BigDecimal("0.2")));
            }

            detail.setSalary(detail.getBaseSalary() != null ? detail.getBaseSalary() : BigDecimal.ZERO);

            // 如果绩效工资不为空，则加上绩效工资
            if (detail.getPerformSalary() != null) {
                detail.setSalary(detail.getSalary().add(detail.getPerformSalary()));
            }
            detail.setTotalSalary(detail.getSalary().add(null == detail.getCommissionAmount() ? BigDecimal.ZERO : detail.getCommissionAmount()));
        }
    }

    private void processDirectorCommissionDetails(String startDate, String endDate, Map<String, CommissionRatioConfig> directorConfigMap, Map<String, CommissionDetail> salesCommissionMap, Map<String, Member> memberMap) {
        if (directorConfigMap == null || directorConfigMap.isEmpty()) {
            return;
        }
        for (Map.Entry<String, CommissionRatioConfig> entry : directorConfigMap.entrySet()) {
            String department = entry.getKey();
            CommissionRatioConfig config = entry.getValue();

            // 查询该部门下的所有实收数据
            YpaidDetails paidDetailsQuery = buildPaidQuery(startDate, endDate, department);
            List<YpaidDetails> departmentPaidDetails = ypaidDetailsMapper.selectPaidByParms(paidDetailsQuery);

            String salesManager = config.getName();
            // 汇总数据到对应的CommissionDetail对象
            for (YpaidDetails detail : departmentPaidDetails) {
                CommissionDetail commission = getOrCreateCommissionDetail(salesCommissionMap, salesManager, memberMap, null, detail.getDepartment());
                BigDecimal ratio = config.getRobotRatio() != null ? config.getRobotRatio() : BigDecimal.ZERO;
                // 累加销售金额
                if (detail.getAmount() != null) {
                    BigDecimal currentAmount = Optional.ofNullable(commission.getSalesAmount()).orElse(BigDecimal.ZERO);
                    commission.setSalesAmount(currentAmount.add(detail.getAmount()));
                    // 记录交易详情
                    List<CommissionComment> commentList = commission.getCommentList();
                    if (null == commentList) {
                        commentList = new ArrayList<>();
                        commission.setCommentList(commentList);
                    }
                    CommissionComment comment = new CommissionComment();
                    comment.setAmount(detail.getAmount());
                    comment.setRatio(ratio);

                    commentList.add(comment);
                }

                // 累加提成金额（根据配置计算）
                BigDecimal commissionAmount = ratio.multiply(detail.getAmount());

                BigDecimal currentCommission = Optional.ofNullable(commission.getCommissionAmount()).orElse(BigDecimal.ZERO);
                commission.setCommissionAmount(currentCommission.add(commissionAmount));
            }
        }

        List<String> directorNames = directorConfigMap.values().stream().map(CommissionRatioConfig::getName).distinct().collect(Collectors.toList());
        for (String name : directorNames) {
            // 新增退款处理逻辑：查询线下网络招商四部，销售经理为config.name的数据
            // 线下网络招商四部 销售经理退款比率SalesCommissionRatio, 电话销售经理退款比率PhoneCommissionRatio
            YpaidDetails specialPaidDetailsQuery = buildSpecialPaidQuery(startDate, endDate, name);
            List<YpaidDetails> specialDepartmentPaidDetails = ypaidDetailsMapper.selectPaidByParms(specialPaidDetailsQuery);
            Map<BigDecimal, List<YpaidDetails>> groupedByRatio = specialDepartmentPaidDetails.stream().collect(Collectors.groupingBy(YpaidDetails::getSalesCommissionRatio));
            CommissionDetail commission = getOrCreateCommissionDetail(salesCommissionMap, name, memberMap, null, specialPaidDetailsQuery.getDepartment());
            for (Map.Entry<BigDecimal, List<YpaidDetails>> groupEntry : groupedByRatio.entrySet()) {
                BigDecimal ratio = groupEntry.getKey();
                List<YpaidDetails> details = groupEntry.getValue();

                // 创建CommissionRefund对象并赋值
                CommissionRefund refund = new CommissionRefund();
                refund.setCommissionRatio(ratio);
                // 根据需要设置其他属性
                // 累加销售金额
                BigDecimal totalSalesAmount = details.stream().map(YpaidDetails::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                refund.setRefundAmount(totalSalesAmount);
                // 四部的实收不累计到总实收中
                // BigDecimal currentAmount = Optional.ofNullable(commission.getSalesAmount()).orElse(BigDecimal.ZERO);
                // commission.setSalesAmount(currentAmount.add(totalSalesAmount));
                // 累加提成金额 按照SalesCommissionRatio计算
                BigDecimal totalCommissionAmount = totalSalesAmount.multiply(ratio);
                BigDecimal currentCommission = Optional.ofNullable(commission.getCommissionAmount()).orElse(BigDecimal.ZERO);
                commission.setCommissionAmount(currentCommission.add(totalCommissionAmount));

                // 将refund添加到commission的refundList中
                List<CommissionRefund> refundList = commission.getRefundList();
                if (refundList == null) {
                    refundList = new ArrayList<>();
                    commission.setRefundList(refundList);
                }
                refundList.add(refund);
            }
        }
    }

    private static YpaidDetails buildPaidQuery(String startDate, String endDate, String department) {
        YpaidDetails paidDetailsQuery = new YpaidDetails();
        paidDetailsQuery.setCollectionDateStart(startDate);
        paidDetailsQuery.setCollectionDateEnd(endDate);
        paidDetailsQuery.setDepartment(department);
        return paidDetailsQuery;
    }

    private static YpaidDetails buildSpecialPaidQuery(String startDate, String endDate, String name) {
        String specialDepartment = "线下网络招商四部";
        YpaidDetails specialPaidDetailsQuery = buildPaidQuery(startDate, endDate, specialDepartment);
        specialPaidDetailsQuery.setSalesManager(name);
        return specialPaidDetailsQuery;
    }

    /**
     * 处理总监线上订单的提成数据
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param directorConfigMap  总监配置映射
     * @param salesCommissionMap 佣金汇总Map
     * @param memberMap          人员信息Map
     */
    private void processDirectorOrderCommissionDetails(String startDate, String endDate, Map<String, CommissionRatioConfig> directorConfigMap, Map<String, CommissionDetail> salesCommissionMap, Map<String, Member> memberMap) {
        if (directorConfigMap == null || directorConfigMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, CommissionRatioConfig> entry : directorConfigMap.entrySet()) {
            String department = entry.getKey();
            CommissionRatioConfig config = entry.getValue();
            // 汇总数据到对应的CommissionDetail对象
            CommissionDetail commission = getOrCreateCommissionDetail(salesCommissionMap, config.getName(), memberMap, config, department);

            //特殊处理 线下网络运营部 单独处理
            if ("线下网络运营部".equals(department)) {
                List<String> nameList = getDepartmentMembers(department);
                if (nameList == null) {
                    continue;
                }
                YorderDetails orderDetailsQuery = new YorderDetails();
                orderDetailsQuery.setShippingDateStart(startDate);
                orderDetailsQuery.setShippingDateEnd(endDate);
                orderDetailsQuery.setPaymentStatus("欠款");
                // 运费
                orderDetailsQuery.setProductCode("YF000101");
                // 审核通过
                orderDetailsQuery.setExp1("1");
                orderDetailsQuery.setSalesmanList(nameList);
                List<YorderDetails> departmentOrderDetails = yorderDetailsMapper.selectOrderByParms(orderDetailsQuery);

                processDirectorCommissionDetail(departmentOrderDetails, commission, config);

                querySetFreightAmount(startDate, endDate, null, nameList, commission);
            } else {
                // 查询该部门下的所有订单数据
                YorderDetails orderDetailsQuery = new YorderDetails();
                orderDetailsQuery.setShippingDateStart(startDate);
                orderDetailsQuery.setShippingDateEnd(endDate);
                orderDetailsQuery.setDepartment(department);
                orderDetailsQuery.setPaymentStatus("欠款");
                // 运费
                orderDetailsQuery.setProductCode("YF000101");
                // 审核通过
                orderDetailsQuery.setExp1("1");
                List<YorderDetails> departmentOrderDetails = yorderDetailsMapper.selectOrderByParms(orderDetailsQuery);

                processDirectorCommissionDetail(departmentOrderDetails, commission, config);

                querySetFreightAmount(startDate, endDate, department, null, commission);
            }
        }
    }

    private List<String> getDepartmentMembers(String department) {
        // 查询当前部门的人员, 通过人员查询订单
        Member paramMember = new Member();
        paramMember.setDepartment(department);
        List<Member> memberList = memberMapper.selectMembers(paramMember);
        if (memberList.isEmpty()) {
            return null;
        }
        List<String> nameList = memberList.stream().map(Member::getName).collect(Collectors.toList());
        return nameList;
    }

    private void querySetFreightAmount(String startDate, String endDate, String department, List<String> nameList, CommissionDetail commission) {
        // 查询运费
        YorderDetails orderDetailsQuery2 = new YorderDetails();
        orderDetailsQuery2.setShippingDateStart(startDate);
        orderDetailsQuery2.setShippingDateEnd(endDate);
        orderDetailsQuery2.setDepartment(department);
        orderDetailsQuery2.setPaymentStatus("欠款");
        orderDetailsQuery2.setProductCode("YF000101");
        orderDetailsQuery2.setExp1("1");
        orderDetailsQuery2.setSalesmanList(nameList);
        BigDecimal currentFreightAmount = Optional.ofNullable(commission.getFreightAmount()).orElse(BigDecimal.ZERO);
        YorderDetails yorderDetails = yorderDetailsMapper.selectOrderFreightByParms(orderDetailsQuery2);
        if (null == yorderDetails) {
            return;
        }
        BigDecimal freightAmount = Optional.ofNullable(yorderDetails.getTaxPrice()).orElse(BigDecimal.ZERO);
        commission.setFreightAmount(currentFreightAmount.add(freightAmount));
    }

    private static void processDirectorCommissionDetail(List<YorderDetails> departmentOrderDetails, CommissionDetail commission, CommissionRatioConfig config) {
        // 按机器人和配件分类汇总
        BigDecimal robotAmount = BigDecimal.ZERO;
        BigDecimal partsAmount = BigDecimal.ZERO;

        for (YorderDetails detail : departmentOrderDetails) {
            // 跳过运费和欠款
            if ("YF000101".equals(detail.getProductCode()) || "欠款".equals(detail.getPaymentStatus())) {
                continue;
            }

            if (detail.getTaxPrice() != null) {
                // 累加订单金额
                BigDecimal currentOrderAmount = Optional.ofNullable(commission.getOrderAmount()).orElse(BigDecimal.ZERO);
                commission.setOrderAmount(currentOrderAmount.add(detail.getTaxPrice()));

                // 根据是否是机器人分别累加机器人金额和配件金额
                if (!StringUtils.isEmpty(detail.getRobotOrNot())) {
                    if ("是".equals(detail.getRobotOrNot())) {
                        // 机器人
                        robotAmount = robotAmount.add(detail.getTaxPrice());
                        BigDecimal currentRobotAmount = Optional.ofNullable(commission.getRobotAmount()).orElse(BigDecimal.ZERO);
                        commission.setRobotAmount(currentRobotAmount.add(detail.getTaxPrice()));
                    } else {
                        // 配件
                        partsAmount = partsAmount.add(detail.getTaxPrice());
                        BigDecimal currentPartsAmount = Optional.ofNullable(commission.getPartsAmount()).orElse(BigDecimal.ZERO);
                        commission.setPartsAmount(currentPartsAmount.add(detail.getTaxPrice()));
                    }
                }
            }
        }

        // 计算总监的提成金额
        BigDecimal robotCommission = robotAmount.multiply(config.getRobotRatio());
        BigDecimal partsCommission = partsAmount.multiply(config.getPartRatio());
        BigDecimal totalCommission = robotCommission.add(partsCommission);

        commission.setRobotRatio(config.getRobotRatio());
        commission.setPartRatio(config.getPartRatio());

        // 累加提成金额
        BigDecimal currentCommissionAmount = Optional.ofNullable(commission.getCommissionAmount()).orElse(BigDecimal.ZERO);
        commission.setCommissionAmount(currentCommissionAmount.add(totalCommission));
    }

    /**
     * 处理订单佣金数据并整合到结果Map中
     *
     * @param orderDetailsList  订单明细列表
     * @param commissionMap     佣金汇总Map
     * @param memberMap         人员信息Map
     * @param directorConfigMap 总监配置Map
     */
    private void processOrderCommissionDetails(
            List<YorderDetails> orderDetailsList,
            Map<String, CommissionDetail> commissionMap,
            Map<String, Member> memberMap,
            Map<String, CommissionRatioConfig> directorConfigMap) {

        // 查询普通员工的订单分成配置
        Map<String, CommissionRatioConfig> normalEmployeeConfigMap = queryNormalEmployeeCommissionConfig(CommissionType.ORDER);
        if (normalEmployeeConfigMap == null) {
            return;
        }

        for (YorderDetails detail : orderDetailsList) {
            String salesman = detail.getSalesman();

            if (StringUtils.isEmpty(salesman)) {
                continue;
            }

            // 总监不处理
            if (directorConfigMap.containsKey(salesman)) {
                continue;
            }

            // 运费和欠款不处理
            if ("YF000101".equals(detail.getProductCode()) || "欠款".equals(detail.getPaymentStatus())) {
                continue;
            }
            Member member = memberMap.get(salesman);
            if (member == null) {
                continue;
            }
            if (null == member.getDepartment()) {
                continue;
            }
            if (!member.getDepartment().equals(detail.getDepartment())) {
                continue;
            }
            if (member.getDepartment().startsWith("幼教")) {
                continue;
            }
            CommissionRatioConfig commissionConfig = normalEmployeeConfigMap.get(detail.getDepartment());

            CommissionDetail commission = getOrCreateCommissionDetail(commissionMap, salesman, memberMap, commissionConfig, detail.getDepartment());

            // 累加订单金额 (tax_price作为orderAmount)
            if (detail.getTaxPrice() != null) {
                BigDecimal currentOrderAmount = Optional.ofNullable(commission.getOrderAmount()).orElse(BigDecimal.ZERO);
                commission.setOrderAmount(currentOrderAmount.add(detail.getTaxPrice()));

                // 根据是否是机器人分别累加机器人金额和配件金额
                if (!StringUtils.isEmpty(detail.getRobotOrNot())) {
                    if ("是".equals(detail.getRobotOrNot())) {
                        // 机器人
                        BigDecimal currentRobotAmount = Optional.ofNullable(commission.getRobotAmount()).orElse(BigDecimal.ZERO);
                        commission.setRobotAmount(currentRobotAmount.add(detail.getTaxPrice()));
                        commission.setRobotRatio(detail.getCommissionRate());
                    } else {
                        // 配件
                        BigDecimal currentPartsAmount = Optional.ofNullable(commission.getPartsAmount()).orElse(BigDecimal.ZERO);
                        commission.setPartsAmount(currentPartsAmount.add(detail.getTaxPrice()));
                        commission.setPartRatio(detail.getCommissionRate());
                    }
                }

                BigDecimal commissionAmount = null != detail.getCommissionAmount() ? detail.getCommissionAmount() : BigDecimal.ZERO;
                // 累加提成金额
                BigDecimal currentCommissionAmount = Optional.ofNullable(commission.getCommissionAmount()).orElse(BigDecimal.ZERO);
                commission.setCommissionAmount(currentCommissionAmount.add(commissionAmount));

                List<CommissionComment> commentList = commission.getCommentList();
                if (null == commentList) {
                    commentList = new ArrayList<>();
                    commission.setCommentList(commentList);
                }
                CommissionComment comment = new CommissionComment();
                comment.setAmount(detail.getTaxPrice());
                comment.setRatio(detail.getCommissionRate());
                comment.setType("是".equals(detail.getRobotOrNot()) ? "机器人" : "配件");

                commentList.add(comment);
            }
        }
    }
    private void processOrderFreightDetails(
            Map<String, CommissionDetail> commissionMap,
            Map<String, Member> memberMap,
            Map<String, CommissionRatioConfig> directorConfigMap, String startDate, String endDate) {

        // 查询普通员工的订单分成配置
        Map<String, CommissionRatioConfig> normalEmployeeConfigMap = queryNormalEmployeeCommissionConfig(CommissionType.ORDER);
        if (normalEmployeeConfigMap == null) {
            return;
        }
        // 查询运费
        YorderDetails orderDetailsQuery2 = new YorderDetails();
        orderDetailsQuery2.setShippingDateStart(startDate);
        orderDetailsQuery2.setShippingDateEnd(endDate);
        orderDetailsQuery2.setPaymentStatus("欠款");
        orderDetailsQuery2.setProductCode("YF000101");
        orderDetailsQuery2.setExp1("1");
        List<YorderDetails> orderDetailsList = yorderDetailsMapper.selectOrderFreightGroupByParms(orderDetailsQuery2);


        for (YorderDetails detail : orderDetailsList) {
            String salesman = detail.getSalesman();

            if (StringUtils.isEmpty(salesman)) {
                continue;
            }

            // 总监不处理
            if (directorConfigMap.containsKey(salesman)) {
                continue;
            }

            // 运费和欠款不处理
            if ("YF000101".equals(detail.getProductCode()) || "欠款".equals(detail.getPaymentStatus())) {
                continue;
            }
            Member member = memberMap.get(salesman);
            if (member == null) {
                continue;
            }
            if (null == member.getDepartment()) {
                continue;
            }
            if (!member.getDepartment().equals(detail.getDepartment())) {
                continue;
            }
            if (member.getDepartment().startsWith("幼教")) {
                continue;
            }

            CommissionRatioConfig commissionConfig = normalEmployeeConfigMap.get(detail.getDepartment());

            CommissionDetail commission = getOrCreateCommissionDetail(commissionMap, salesman, memberMap, commissionConfig, detail.getDepartment());


            BigDecimal currentFreightAmount = Optional.ofNullable(commission.getFreightAmount()).orElse(BigDecimal.ZERO);
            BigDecimal freightAmount = Optional.ofNullable(detail.getTaxPrice()).orElse(BigDecimal.ZERO);
            commission.setFreightAmount(currentFreightAmount.add(freightAmount));

        }
    }

    private void processCommissionDetails(
            List<YpaidDetails> detailsList,
            Map<String, CommissionDetail> commissionMap,
            Map<String, Member> memberMap,
            boolean isSalesManager,
            Map<String, CommissionRatioConfig> directorConfigMap) {

        for (YpaidDetails detail : detailsList) {
            String saler = isSalesManager ? detail.getSalesManager() : detail.getPhoneSales();

            if (StringUtils.isEmpty(saler)) {
                continue;
            }
            // 总监不处理
            if (directorConfigMap.containsKey(saler)) {
                continue;
            }

            Member member = memberMap.get(saler);
            if (member == null) {
                // 用户为空,则跳过
                continue;
            }
            CommissionDetail commission = getOrCreateCommissionDetail(commissionMap, saler, memberMap, null, detail.getDepartment());

            // 累加销售金额
            if (detail.getAmount() != null) {
                BigDecimal currentAmount = Optional.ofNullable(commission.getSalesAmount()).orElse(BigDecimal.ZERO);
                commission.setSalesAmount(currentAmount.add(detail.getAmount()));
            }

            // 累加提成金额
            BigDecimal commissionAmount = isSalesManager ? detail.getSalesCommissionAmount() : detail.getPhoneCommissionAmount();
            if (commissionAmount != null) {
                BigDecimal currentCommission = Optional.ofNullable(commission.getCommissionAmount()).orElse(BigDecimal.ZERO);
                commission.setCommissionAmount(currentCommission.add(commissionAmount));
            }
            List<CommissionComment> commentList = commission.getCommentList();
            if (null == commentList) {
                commentList = new ArrayList<>();
                commission.setCommentList(commentList);
            }
            CommissionComment comment = new CommissionComment();
            comment.setAmount(detail.getAmount());
            comment.setRatio(detail.getSalesCommissionRatio());
            if (!isSalesManager) {
                comment.setRatio(detail.getPhoneCommissionRatio());
                comment.setType("电话招商成交");
            }
            commentList.add(comment);
        }
    }

    /**
     * 查询总监分成比率配置
     *
     * @param type 类型：1-实收，2-订单
     * @return 总监分成比率配置列表
     */
    private List<CommissionRatioConfig> queryDirectorCommissionRatioConfig(CommissionType type) {
        // 查询所有分成比率配置
        List<CommissionRatioConfig> commissionRatioConfigs = commissionRatioConfigMapper.selectAll();
        // 过滤出总监的配置（非"其他"类型的配置）
        List<CommissionRatioConfig> directorConfigs = new ArrayList<>();

        for (CommissionRatioConfig config : commissionRatioConfigs) {
            // 根据传入的类型过滤
            if (type.getValue().equals(config.getType())) {
                // 总监级特殊处理（非"其他"类型的配置）
                if (!CommissionRatioConfig.OTHER_NAME.equals(config.getName())) {
                    directorConfigs.add(config);
                }
            }
        }

        return directorConfigs;
    }

    /**
     * 查询普通员工分成配置
     *
     * @param type 类型：1-实收，2-订单
     * @return 普通员工分成配置
     */
    private Map<String, CommissionRatioConfig> queryNormalEmployeeCommissionConfig(CommissionType type) {
        // 查询所有分成比率配置
        List<CommissionRatioConfig> commissionRatioConfigs = commissionRatioConfigMapper.selectAll();
        // departmentName为key，config为value
        Map<String, CommissionRatioConfig> normalEmployeeConfigMap = new HashMap<>();
        // 查找"其他"类型的配置，这是普通员工的配置
        for (CommissionRatioConfig config : commissionRatioConfigs) {
            if (type.getValue().equals(config.getType()) && CommissionRatioConfig.OTHER_NAME.equals(config.getName())) {
                normalEmployeeConfigMap.put(config.getDepartmentName(), config);
            }
        }

        return normalEmployeeConfigMap;
    }

    /**
     * 查询哪些是总监
     *
     * @param type 类型：1-实收，2-订单
     * @return 总监映射表，key为部门名称，value为对应的总监配置
     */
    private Map<String, CommissionRatioConfig> queryDirectorMap(CommissionType type) {
        // 获取总监配置列表
        List<CommissionRatioConfig> directorConfigs = queryDirectorCommissionRatioConfig(type);

        // 创建部门到总监配置的映射
        Map<String, CommissionRatioConfig> directorMap = new HashMap<>();
        for (CommissionRatioConfig config : directorConfigs) {
            // 使用部门名称作为key，总监配置作为value
            directorMap.put(config.getDepartmentName(), config);
        }

        return directorMap;
    }

    /**
     * 总监姓名
     * @return 总监姓名映射表
     */
    private Map<String, CommissionRatioConfig> queryDirectorNameMap() {
        // 获取总监配置列表
        List<CommissionRatioConfig> directorConfigs = queryDirectorCommissionRatioConfig(CommissionType.REAL);
        List<CommissionRatioConfig> directorConfigs2 = queryDirectorCommissionRatioConfig(CommissionType.ORDER);
        directorConfigs.addAll(directorConfigs2);
        // 创建部门到总监配置的映射
        Map<String, CommissionRatioConfig> directorMap = new HashMap<>();
        for (CommissionRatioConfig config : directorConfigs) {
            // 使用部门名称作为key，总监配置作为value
            directorMap.put(config.getName(), config);
        }

        return directorMap;
    }

    /**
     * 获取或创建CommissionDetail对象
     * 抽象公共方法，用于从Map中获取CommissionDetail对象，如果不存在则创建一个新的
     *
     * @param commissionMap    存储CommissionDetail的Map
     * @param key              用于查找CommissionDetail的键
     * @param memberMap        成员信息Map
     * @param commissionConfig 可选的佣金配置，用于设置机器人比例和配件比例
     * @return CommissionDetail对象
     */
    private CommissionDetail getOrCreateCommissionDetail(
            Map<String, CommissionDetail> commissionMap,
            String key,
            Map<String, Member> memberMap,
            CommissionRatioConfig commissionConfig, String orderDepartment) {

        return commissionMap.computeIfAbsent(key, k -> {
            CommissionDetail cd = new CommissionDetail();
            Member member = memberMap.get(k);
            if (member != null) {
                cd.setMember(member);
            } else {
                log.info("找不到用户 使用实收表姓名+部门数据 姓名=" + k);
                Member member1 = new Member();
                member1.setName(k);
//                member1.setDepartment(orderDepartment);
                cd.setMember(member1);
            }
            // 如果提供了佣金配置，设置机器人比例和配件比例
            if (commissionConfig != null) {
                cd.setRobotRatio(null == commissionConfig.getRobotRatio() ? BigDecimal.ZERO : commissionConfig.getRobotRatio());
                cd.setPartRatio(null == commissionConfig.getPartRatio() ? BigDecimal.ZERO : commissionConfig.getPartRatio());
            }
            return cd;
        });
    }
}


/* Location:              D:\desktopFiles\webapps\ROOT\WEB-INF\classes\!\com\efrobot\robotstore\manager\service\impl\InfoServiceImpl.class
 * Java compiler version: 7 (51.0)
 * JD-Core Version:       1.1.3
 */
