 package com.efrobot.robotstore.util;
 
 import java.text.DateFormat;
 import java.text.ParseException;
 import java.text.SimpleDateFormat;
 import java.util.Calendar;
 import java.util.Date;
 import java.util.TimeZone;
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 public class DateUtils
 {
   public static final String yyyy_MM = "yyyy-MM";
   public static final String DATE_PATTERN = "yyyy-MM-dd";
   public static SimpleDateFormat DATE_PATTERN_SimpleDateFormat=new SimpleDateFormat(DATE_PATTERN);
   public static final String DATE_PATTERN3 = "yyyy/MM/dd";
   public static final String LONG_DATE_FORMAT = "yyyy-MM-dd";
   public static final String DATE_PATTERN2 = "yyyy年MM月dd日";
   public static final String DATE_hhmm = "HH:mm";
   public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
   
   public static String format(Date date) {
     try {
       return format(date, "yyyy-MM-dd");
     } catch (Exception e) {
       return null;
     } 
   }
 
 
 
 
   
   public static String format2(Date date) {
     try {
       return format(date, "yyyy年MM月dd日");
     } catch (Exception e) {
       return null;
     } 
   }
 
 
 
 
   
   public static String format3(Date date) {
     try {
       return format(date, "yyyy/MM/dd");
     } catch (Exception e) {
       return null;
     } 
   }
 
 
 
 
 
 
   
   public static String format(Date date, String pattern) {
     if (date != null) {
       SimpleDateFormat df = new SimpleDateFormat(pattern);
       return df.format(date);
     } 
     return null;
   }
 

   
   public static Date CSTToDate(String sDate) {
     SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
     Date date = null;
     try {
       date = format.parse(sDate);
     } catch (ParseException e) {
       
       e.printStackTrace();
     } 
     return date;
   }

   public static Date CSTToDate(String sDate,SimpleDateFormat mSimpleDateFormat) {
     Date date = null;
     try {
       date = mSimpleDateFormat.parse(sDate);
     } catch (ParseException e) {

       e.printStackTrace();
     }
     return date;
   }


   public static boolean isOrNoOneDay(long timestamp) {
     long current = System.currentTimeMillis();
     long zero = current / 86400000L * 86400000L - TimeZone.getDefault().getRawOffset();
     if (timestamp > zero) return true; 
     return false;
   }
   
   public static long getZeroDate() {
     long current = System.currentTimeMillis();
     return current / 86400000L * 86400000L - TimeZone.getDefault().getRawOffset();
   }
   
   public static long getZeroDate(long timestamp) {
     long current = timestamp;
     return current / 86400000L * 86400000L - TimeZone.getDefault().getRawOffset();
   }
   
   public static String convertTo(String time) {
     String[] ts = time.split(":");
     if (ts != null && ts.length > 0) {
       String ret = ""; byte b; int i; String[] arrayOfString;
       for (i = (arrayOfString = ts).length, b = 0; b < i; ) { String d = arrayOfString[b];
         ret = String.valueOf(ret) + d; b++; }
       
       return ret;
     } 
     return "";
   }
 
 
   
   public static Date getStartTime() {
     return getStartTime(new Date());
   }
   
   public static Date getEndTime() {
     return getEndTime(new Date());
   }
   
   public static Date getStartTime(Date date) {
     Calendar cal1 = Calendar.getInstance();
     cal1.setTime(date);
     
     cal1.set(11, 0);
     cal1.set(12, 0);
     cal1.set(13, 0);
     cal1.set(14, 0);
     return cal1.getTime();
   }
   
   public static Date getEndTime(Date date) {
     Calendar cal1 = Calendar.getInstance();
     cal1.setTime(date);
     
     cal1.set(11, 23);
     cal1.set(12, 59);
     cal1.set(13, 59);
     cal1.set(14, 59);
     return cal1.getTime();
   }
   
   public static int getTwoDateDay(Date startDate, Date endDate) {
     SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
     long startDateTime = 0L;
     long endDateTime = 0L;
     try {
       startDateTime = dateFormat.parse(dateFormat.format(startDate)).getTime();
       endDateTime = dateFormat.parse(dateFormat.format(endDate)).getTime();
     } catch (ParseException e) {
       e.printStackTrace();
     } 
     
     return (int)((startDateTime - endDateTime) / 86400000L);
   }
   
   public static Date optDate(Date data, int day) {
     DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
     Date temp_date = null;
     try {
       Calendar c = Calendar.getInstance();
       c.setTime(data);
       c.add(5, day);
       temp_date = c.getTime();
     }
     catch (Exception e) {
       e.printStackTrace();
     } 
     return temp_date;
   }

   public static Date parseDate(String startDate, String s) {
     return CSTToDate(startDate, new SimpleDateFormat(s));
   }
 }


/* Location:              D:\desktopFiles\webapps\ROOT\WEB-INF\classes\!\com\efrobot\robotstor\\util\DateUtils.class
 * Java compiler version: 7 (51.0)
 * JD-Core Version:       1.1.3
 */
